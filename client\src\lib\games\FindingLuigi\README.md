# Finding Luigi Game

A multiplayer "Finding Luigi"-style hidden object game built with Phaser 3 and Socket.IO.

## Game Overview

Players must find specific target faces scattered throughout a scene as quickly as possible. The game features:

- **Multiplayer Support**: Real-time multiplayer with server-side validation
- **Round-based Gameplay**: Multiple rounds with increasing difficulty
- **SVG Face Assets**: Scalable vector graphics for crisp visuals
- **Scoring System**: Points for correct finds, penalties for mistakes
- **Time Pressure**: Limited time per round adds excitement

## Game Flow

1. **Initialization**: Server generates random face layout and assigns targets
2. **Countdown**: 3-2-1-GO countdown before game starts
3. **Gameplay**: Players click on faces to find their assigned targets
4. **Validation**: Server validates all clicks to prevent cheating
5. **Round Completion**: New round starts when all targets found
6. **Game End**: Game ends when time runs out or lives depleted

## Technical Architecture

### Client-Side (Phaser 3)
- **PreloadScene**: Loads all SVG assets and UI elements
- **GameStartScene**: Shows instructions and start button
- **GameScene**: Main gameplay with face sprites and target UI
- **GameEndScene**: Shows final score and play again option

### Server-Side (Node.js)
- **FindingLuigiController**: Handles game logic and validation
- **Socket Events**: Real-time communication for game state
- **Authoritative State**: Server maintains game state to prevent cheating

### Communication
- **Socket.IO**: Real-time bidirectional communication
- **Event-driven**: Game actions trigger server validation
- **State Sync**: Client receives validated game state updates

## Assets

### Face Assets (`/assets-find-someone/faces/`)
- `normal_face.svg` - Basic face
- `blue_hat_face.svg` - Face with blue hat
- `frenna_face.svg` - Frenna character face
- `red_flower_face.svg` - Face with red flower

### Background Assets (`/assets-find-someone/backgrounds/`)
- `forest.svg` - Forest scene with trees
- `city.svg` - Urban cityscape
- `park.svg` - Park with grass and benches

### UI Assets (`/assets-find-someone/ui/`)
- `target_frame.svg` - Golden frame for target display
- `checkmark.svg` - Green checkmark for found targets

## Configuration

### Game Settings (`config/GameConfig.ts`)
- Game dimensions: 540x960
- Face count: 25 faces per layout
- Targets per round: 3
- Round time: 30 seconds
- Scoring: 100 points per target, penalties for mistakes

### Visual Settings
- Face scale range: 0.6 - 1.4
- Rotation range: -15° to +15°
- Minimum distance between faces: 80px
- Hover effects and animations

## Socket Events

### Client → Server
- `init`: Initialize game session
- `start`: Start game after countdown
- `action`: Send face selection with validation data
- `end`: End game manually

### Server → Client
- `initialized`: Game initialized with first round data
- `started`: Game started successfully
- `action_result`: Result of face selection (correct/incorrect)
- `ended`: Game ended with final score
- `error`: Error messages

## Game States

- **LOADING**: Assets being loaded
- **WAITING**: Waiting for game initialization
- **COUNTDOWN**: 3-2-1 countdown active
- **PLAYING**: Active gameplay
- **ROUND_COMPLETE**: Round finished, transitioning
- **GAME_COMPLETE**: Game finished
- **PAUSED**: Game temporarily paused

## Scoring System

- **Target Found**: +100 points
- **Time Bonus**: Up to +60 points for quick finds (under 3 seconds)
- **Round Completion**: +200 bonus points
- **Wrong Selection**: -10 points and lose 1 life
- **Lives**: Start with 3, game ends at 0

## Development

### Adding New Faces
1. Add SVG file to `/assets-find-someone/faces/`
2. Update `FACE_ASSETS` in `config/GameConfig.ts`
3. Add face name to `FaceName` type in `types/types.ts`
4. Update server-side `FACE_NAMES` array

### Adding New Backgrounds
1. Add image/SVG to `/assets-find-someone/backgrounds/`
2. Update `BACKGROUND_ASSETS` in `config/GameConfig.ts`
3. Server can randomly select backgrounds per round

### Customizing Gameplay
- Modify constants in `config/GameConfig.ts`
- Adjust scoring in server-side `FINDING_LUIGI_CONSTANTS`
- Change layout generation in server controller
- Add new game mechanics in `GameScene.ts`

## Future Enhancements

- **Power-ups**: Special abilities like hints or time extensions
- **Difficulty Levels**: Adjustable face count and time limits
- **Themes**: Different visual themes and asset sets
- **Achievements**: Unlock system for special accomplishments
- **Leaderboards**: Global and room-based high scores
- **Spectator Mode**: Watch other players' games
- **Custom Rooms**: Private rooms with custom settings
