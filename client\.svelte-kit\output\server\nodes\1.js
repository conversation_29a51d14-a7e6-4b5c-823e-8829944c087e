

export const index = 1;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/fallbacks/error.svelte.js')).default;
export const imports = ["_app/immutable/nodes/1.CCDFjfsy.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/Dh9ZwQ9u.js","_app/immutable/chunks/CBPGQi6i.js","_app/immutable/chunks/D7HYObST.js","_app/immutable/chunks/XXcWJRIr.js","_app/immutable/chunks/CbN9a2Ga.js"];
export const stylesheets = [];
export const fonts = [];
