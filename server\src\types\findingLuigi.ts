/**
 * Finding Luigi Game Types and Interfaces
 * Server-side types for the Finding Luigi hidden object game
 */

import type { BaseActionResult, GameActionData } from './game';

// Face types available in the game
export type FaceName = 'normal_face' | 'blue_hat_face' | 'frenna_face' | 'red_flower_face';

// Position data for face sprites
export interface FacePosition {
  x: number;
  y: number;
  scale: number; // Random scale between 0.8-1.2 for variety
  rotation: number; // Random rotation for natural look
}

// Face data structure sent to client (secure - no target info)
export interface ClientFaceData {
  id: string; // Unique random ID for this face
  faceType: FaceName;
  position: FacePosition;
  zIndex: number; // For layering
}

// Server-only face data structure (includes target info)
export interface ServerFaceData extends ClientFaceData {
  isTarget: boolean; // Whether this face is a target for any player
  targetForPlayers: string[]; // Array of player IDs who need to find this face
}

// Game layout data sent to client
export interface ClientGameLayout {
  faces: ClientFaceData[];
  backgroundKey: string;
  layoutSeed: string; // For reproducible layouts
}

// Server-only game layout (includes target info)
export interface ServerGameLayout extends ClientGameLayout {
  faces: ServerFaceData[];
  playerTargets: Map<string, string[]>; // playerId -> array of target face IDs
}

// Round data for Finding Luigi
export interface FindingLuigiRoundData {
  roundNumber: number;
  layout: ClientGameLayout;
  targets: FaceName[]; // Target faces for this player
  timeLimit: number; // in milliseconds
  startTime: number;
  targetsRemaining: number;
}

// Game state specific to Finding Luigi
export interface FindingLuigiGameState {
  currentRound: FindingLuigiRoundData | null;
  roundsCompleted: number;
  isRoundActive: boolean;
  roundStartTime: number | null;
  targetsFound: string[]; // Array of found target face IDs
  targetsRemaining: number;
  currentLayout: ServerGameLayout | null;
}

// Action types for Finding Luigi
export type FindingLuigiActionType = 'face_select';

// Face selection action data
export interface FaceSelectActionData extends GameActionData {
  action: {
    type: 'face_select';
    data: {
      faceId: string; // ID of the selected face
      reactionTime?: number; // time taken to find and select the face
      clickTime: number; // timestamp when face was clicked
      clickPosition: { x: number; y: number }; // where on screen the click occurred
    };
  };
}

// Action result for face selection
export interface FaceSelectResult extends BaseActionResult {
  faceId: string;
  wasTarget: boolean;
  targetFaceType?: FaceName;
  targetsRemaining: number;
  nextRound?: FindingLuigiRoundData;
  roundComplete: boolean;
}

// Socket event data structures specific to Finding Luigi
export interface FindingLuigiStartData {
  roomId: string;
  gameId: string;
}

export interface FindingLuigiGameStartedData {
  gameState: {
    score: number;
    lives: number;
    isActive: boolean;
    startTime?: number;
  };
  firstRound: FindingLuigiRoundData;
  message: string;
}

export interface FindingLuigiActionResultData {
  actionType: 'face_select';
  data: {
    faceId: string;
    wasTarget: boolean;
    targetFaceType?: FaceName;
    isCorrect: boolean;
    points: number;
    newScore: number;
    newLives: number;
    gameEnded: boolean;
    targetsRemaining: number;
    nextRound?: FindingLuigiRoundData;
    roundComplete: boolean;
  };
}

// Constants for Finding Luigi - Arcade Grid Progression
export const FINDING_LUIGI_CONSTANTS = {
  ROUND_TIME: 30, // 30 seconds per round

  // Grid-based progression
  GRID: {
    START_SIZE: 2, // Start with 2x2 grid (4 faces)
    MAX_SIZE: 6, // Maximum 6x6 grid (36 faces)
    CELL_SIZE: 80, // Size of each grid cell
    SPACING: 10, // Space between grid cells
  },

  // Movement progression
  MOVEMENT: {
    MOTION_START_LEVEL: 5, // Start movement at level 5
    SPEED_BASE: 30, // Base movement speed
    SPEED_INCREASE: 10, // Speed increase per level
    DIRECTION_CHANGE_INTERVAL: 3000, // Change direction every 3 seconds
  },

  // Target face (one per level)
  TARGET_FACE: 'frenna_face' as FaceName,

  LAYOUT: {
    MIN_SCALE: 0.9,
    MAX_SCALE: 1.1,
    PADDING: 40, // Minimum distance from screen edges
  },

  SCORING: {
    TARGET_FOUND: 100, // Base points for finding target
    LEVEL_MULTIPLIER: 10, // Additional points per level
    WRONG_FACE_PENALTY: 25, // Points deducted for clicking wrong face
    TIME_BONUS_MAX: 50, // Max bonus for quick finds
    TIME_BONUS_THRESHOLD: 3000, // Under 3 seconds for bonus
  },

  LIVES: {
    INITIAL_LIVES: 3,
    DEDUCT_ON_WRONG: 1,
  },

  MAX_LEVELS: 20 // Maximum levels in arcade mode
} as const;

// Face definitions (matching client-side assets)
export const FACE_NAMES: readonly FaceName[] = [
  'normal_face', 
  'blue_hat_face', 
  'frenna_face', 
  'red_flower_face'
] as const;

// Background options for variety
export const BACKGROUND_KEYS = [
  'forest_background',
  'city_background', 
  'park_background'
] as const;
