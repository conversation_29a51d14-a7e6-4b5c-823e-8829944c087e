/**
 * Level Generator for Number Connect Game
 * Based on the proven solvability algorithm from main.html
 * Generates puzzles that are guaranteed to be solvable
 */

import type { Position, GridCell, GeneratedLevel } from './types/types';

export class LevelGenerator {
  private gridSize: number;

  constructor(gridSize: number = 5) {
    this.gridSize = gridSize;
  }
  
  /**
   * Get the grid size
   */
  getGridSize(): number {
    return this.gridSize;
  }

  /**
   * Generate a solvable puzzle with guaranteed solution path
   * Uses the proven algorithm from main.html for maximum reliability
   * @returns GeneratedLevel object containing the puzzle data
   */
  generateSolvablePuzzle(): GeneratedLevel {
    let attempts = 0;
    const maxAttempts = 1000;
    
    while (attempts < maxAttempts) {
      attempts++;
      
      // Generate a random starting position
      const availableCells: Position[] = [];
      for (let r = 0; r < this.gridSize; r++) {
        for (let c = 0; c < this.gridSize; c++) {
          availableCells.push({ row: r, col: c });
        }
      }

      const startIndex = Math.floor(Math.random() * availableCells.length);
      const start = availableCells[startIndex];
      
      // Find a complete path that covers all cells using the proven algorithm
      const path = this.findCompletePath(start, {}, [start]);
      
      if (path && path.length === this.gridSize * this.gridSize) {
        // Place numbers along the path
        const numberPositions = this.placeNumbers(path);
        
        // Double-check solvability
        if (this.validateSolution(path, numberPositions)) {
          return {
            grid: this.initializeGrid(), // Return empty grid structure
            solutionPath: path,
            numberPositions,
            maxNumber: 5
          };
        }
      }
    }

    // Fallback: generate a simple path if complex algorithm fails
    console.warn('Complex path generation failed after', maxAttempts, 'attempts, using fallback method');
    return this.generateFallbackPuzzle();
  }

  /**
   * Initialize empty grid
   */
  private initializeGrid(): GridCell[][] {
    return Array(this.gridSize)
      .fill(null)
      .map(() =>
        Array(this.gridSize)
          .fill(null)
          .map(() => ({ visited: false, number: null, hasPath: false }))
      );
  }

  /**
   * Find a complete path that visits all cells exactly once
   * Uses backtracking algorithm to ensure solvability
   */
  private findCompletePath(
    currentPos: Position,
    visited: { [key: string]: boolean },
    path: Position[]
  ): Position[] | null {
    // Base case: if we've visited all cells, return the path
    if (path.length === this.gridSize * this.gridSize) {
      return path;
    }

    const { row, col } = currentPos;
    const key = `${row}-${col}`;
    visited[key] = true;

    // Get shuffled neighbors to add randomness
    const neighbors = this.shuffleArray(this.getNeighbors(row, col));

    // Try each neighbor
    for (const neighbor of neighbors) {
      const nKey = `${neighbor.row}-${neighbor.col}`;
      if (!visited[nKey]) {
        const result = this.findCompletePath(
          neighbor,
          { ...visited }, // Create new visited copy for backtracking
          [...path, neighbor] // Create new path copy
        );
        if (result) return result;
      }
    }

    return null;
  }

  /**
   * Get valid neighboring cells (adjacent only, no diagonals)
   */
  private getNeighbors(row: number, col: number): Position[] {
    const neighbors: Position[] = [];
    
    // Up
    if (row > 0) neighbors.push({ row: row - 1, col });
    // Down
    if (row < this.gridSize - 1) neighbors.push({ row: row + 1, col });
    // Left
    if (col > 0) neighbors.push({ row, col: col - 1 });
    // Right
    if (col < this.gridSize - 1) neighbors.push({ row, col: col + 1 });

    return neighbors;
  }

  /**
   * Shuffle array using Fisher-Yates algorithm
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * Place numbers 1-5 along the solution path at strategic positions
   * Uses the same spacing algorithm as main.html for consistency
   */
  private placeNumbers(path: Position[]): { [key: number]: Position } {
    const numberPositions: { [key: number]: Position } = {};
    const pathLength = path.length;
    
    // Use the exact same number placement as main.html for proven solvability
    const numberSteps = [0, 6, 12, 18, 24]; // evenly spaced indices from main.html
    
    // If path is shorter than expected, adjust the spacing proportionally
    const adjustedSteps = numberSteps.map(step => {
      if (pathLength <= 25) {
        return Math.min(step, pathLength - 1);
      }
      return Math.floor((step / 24) * (pathLength - 1));
    });

    for (let i = 0; i < adjustedSteps.length && i < 5; i++) {
      const stepIndex = Math.max(0, Math.min(adjustedSteps[i], pathLength - 1));
      const position = path[stepIndex];
      const number = i + 1;
      
      numberPositions[number] = position;
    }

    return numberPositions;
  }

  /**
   * Generate a fallback puzzle using a simple snake pattern
   * This ensures we always have a solvable puzzle even if the complex algorithm fails
   * Uses a guaranteed solvable pattern
   */
  private generateFallbackPuzzle(): GeneratedLevel {
    const grid = this.initializeGrid();
    const path: Position[] = [];
    
    // Generate a simple snake pattern path (guaranteed solvable)
    for (let row = 0; row < this.gridSize; row++) {
      if (row % 2 === 0) {
        // Left to right
        for (let col = 0; col < this.gridSize; col++) {
          path.push({ row, col });
        }
      } else {
        // Right to left
        for (let col = this.gridSize - 1; col >= 0; col--) {
          path.push({ row, col });
        }
      }
    }

    const numberPositions = this.placeNumbers(path);
    
    // Validate the fallback solution
    if (!this.validateSolution(path, numberPositions)) {
      console.error('Fallback puzzle generation failed validation!');
    }

    return {
      grid: this.initializeGrid(),
      solutionPath: path,
      numberPositions,
      maxNumber: 5
    };
  }

  /**
   * Validate that a given path is actually solvable
   * Uses the same validation logic as main.html for consistency
   */
  validateSolution(path: Position[], numberPositions: { [key: number]: Position }): boolean {
    if (path.length !== this.gridSize * this.gridSize) {
      console.warn('Path length validation failed:', path.length, 'vs expected', this.gridSize * this.gridSize);
      return false;
    }

    // Check that the path visits all cells exactly once
    const visitedCells = new Set<string>();
    for (const pos of path) {
      const key = `${pos.row}-${pos.col}`;
      if (visitedCells.has(key)) {
        console.warn('Cell visited twice:', key);
        return false; // Cell visited twice
      }
      visitedCells.add(key);
    }

    // Check that all grid positions are covered
    for (let row = 0; row < this.gridSize; row++) {
      for (let col = 0; col < this.gridSize; col++) {
        const key = `${row}-${col}`;
        if (!visitedCells.has(key)) {
          console.warn('Cell not visited:', key);
          return false; // Cell not visited
        }
      }
    }

    // Check that numbers 1-5 appear in the correct order along the path
    let expectedNumber = 1;
    const foundNumbers: number[] = [];
    
    for (const pos of path) {
      // Check if this position has a number
      for (let num = 1; num <= 5; num++) {
        const numberPos = numberPositions[num];
        if (numberPos && numberPos.row === pos.row && numberPos.col === pos.col) {
          foundNumbers.push(num);
          if (num === expectedNumber) {
            expectedNumber++;
          }
          break;
        }
      }
    }

    // Verify all numbers 1-5 were found in correct sequence
    const allNumbersFound = foundNumbers.length === 5 && expectedNumber > 5;
    if (!allNumbersFound) {
      console.warn('Number sequence validation failed. Found:', foundNumbers, 'Expected next:', expectedNumber);
    }

    return allNumbersFound;
  }

  /**
   * Get difficulty rating for a generated level
   * Based on path complexity and number placement
   */
  getDifficulty(level: GeneratedLevel): 'easy' | 'medium' | 'hard' {
    const path = level.solutionPath;
    let changeCount = 0;
    
    // Count direction changes in the path
    for (let i = 1; i < path.length - 1; i++) {
      const prev = path[i - 1];
      const curr = path[i];
      const next = path[i + 1];
      
      const dir1 = { row: curr.row - prev.row, col: curr.col - prev.col };
      const dir2 = { row: next.row - curr.row, col: next.col - curr.col };
      
      if (dir1.row !== dir2.row || dir1.col !== dir2.col) {
        changeCount++;
      }
    }
    
    const totalCells = this.gridSize * this.gridSize;
    const changeRatio = changeCount / totalCells;
    
    if (changeRatio < 0.3) return 'easy';
    if (changeRatio < 0.6) return 'medium';
    return 'hard';
  }
  
  /**
   * Generate multiple puzzles and return the best one based on difficulty preference
   */
  generatePuzzleWithDifficulty(targetDifficulty: 'easy' | 'medium' | 'hard' = 'medium'): GeneratedLevel {
    const maxAttempts = 10;
    let bestPuzzle: GeneratedLevel | null = null;
    
    for (let i = 0; i < maxAttempts; i++) {
      const puzzle = this.generateSolvablePuzzle();
      const difficulty = this.getDifficulty(puzzle);
      
      if (difficulty === targetDifficulty) {
        return puzzle; // Found exact match
      }
      
      if (!bestPuzzle) {
        bestPuzzle = puzzle;
      }
    }
    
    return bestPuzzle || this.generateFallbackPuzzle();
  }
}
