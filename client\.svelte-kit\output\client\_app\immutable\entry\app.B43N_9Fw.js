const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["../nodes/0.GHmX8_TY.js","../chunks/DsnmJJEf.js","../chunks/CBPGQi6i.js","../assets/0.PIix4Fxn.css","../nodes/1.CCDFjfsy.js","../chunks/Dh9ZwQ9u.js","../chunks/D7HYObST.js","../chunks/XXcWJRIr.js","../chunks/CbN9a2Ga.js","../nodes/2.CBmOZecg.js","../chunks/6c5zsAwx.js","../chunks/CagF9jJf.js","../assets/MumsNumbers.Cfre7piE.css","../assets/2.BN6ceEAQ.css","../nodes/3.DQDtIKgH.js","../chunks/sPbygbFs.js","../chunks/BZyaMUDL.js"])))=>i.map(i=>d[i]);
import{p as R,i as p,b as O,_ as E}from"../chunks/6c5zsAwx.js";import{x as D,y as q,w as K,E as Q,D as Y,F as z,G as B,I as H,L as J,K as U,V as P,a8 as W,g as f,a9 as X,Q as Z,T as $,p as tt,u as et,h as rt,aa as T,ab as at,f as L,a as x,s as st,b as m,c as nt,q as k,d as ot,r as ct,ac as w,ad as it,t as ut}from"../chunks/CBPGQi6i.js";import{b as ft,m as lt,u as dt,o as mt,s as _t}from"../chunks/D7HYObST.js";import"../chunks/DsnmJJEf.js";function A(c,t,a){D&&q();var n=c,s,r,e=null,o=null;function y(){r&&(U(r),r=null),e&&(e.lastChild.remove(),n.before(e),e=null),r=o,o=null}K(()=>{if(s!==(s=t())){var _=H();if(s){var h=n;_&&(e=document.createDocumentFragment(),e.append(h=Y())),o=z(()=>a(h,s))}_?B.add_callback(y):y()}},Q),D&&(n=J)}function ht(c){return class extends vt{constructor(t){super({component:c,...t})}}}class vt{#e;#t;constructor(t){var a=new Map,n=(r,e)=>{var o=$(e,!1,!1);return a.set(r,o),o};const s=new Proxy({...t.props||{},$$events:{}},{get(r,e){return f(a.get(e)??n(e,Reflect.get(r,e)))},has(r,e){return e===W?!0:(f(a.get(e)??n(e,Reflect.get(r,e))),Reflect.has(r,e))},set(r,e,o){return P(a.get(e)??n(e,o),o),Reflect.set(r,e,o)}});this.#t=(t.hydrate?ft:lt)(t.component,{target:t.target,anchor:t.anchor,props:s,context:t.context,intro:t.intro??!1,recover:t.recover}),(!t?.props?.$$host||t.sync===!1)&&X(),this.#e=s.$$events;for(const r of Object.keys(this.#t))r==="$set"||r==="$destroy"||r==="$on"||Z(this,r,{get(){return this.#t[r]},set(e){this.#t[r]=e},enumerable:!0});this.#t.$set=r=>{Object.assign(s,r)},this.#t.$destroy=()=>{dt(this.#t)}}$set(t){this.#t.$set(t)}$on(t,a){this.#e[t]=this.#e[t]||[];const n=(...s)=>a.call(this,...s);return this.#e[t].push(n),()=>{this.#e[t]=this.#e[t].filter(s=>s!==n)}}$destroy(){this.#t.$destroy()}}const kt={};var gt=L('<div id="svelte-announcer" aria-live="assertive" aria-atomic="true" style="position: absolute; left: 0; top: 0; clip: rect(0 0 0 0); clip-path: inset(50%); overflow: hidden; white-space: nowrap; width: 1px; height: 1px"><!></div>'),bt=L("<!> <!>",1);function yt(c,t){tt(t,!0);let a=R(t,"components",23,()=>[]),n=R(t,"data_0",3,null),s=R(t,"data_1",3,null);et(()=>t.stores.page.set(t.page)),rt(()=>{t.stores,t.page,t.constructors,a(),t.form,n(),s(),t.stores.page.notify()});let r=T(!1),e=T(!1),o=T(null);mt(()=>{const i=t.stores.page.subscribe(()=>{f(r)&&(P(e,!0),at().then(()=>{P(o,document.title||"untitled page",!0)}))});return P(r,!0),i});const y=w(()=>t.constructors[1]);var _=bt(),h=x(_);{var I=i=>{var u=k();const v=w(()=>t.constructors[0]);var g=x(u);A(g,()=>f(v),(l,d)=>{O(d(l,{get data(){return n()},get form(){return t.form},get params(){return t.page.params},children:(b,Pt)=>{var C=k(),S=x(C);A(S,()=>f(y),(G,M)=>{O(M(G,{get data(){return s()},get form(){return t.form},get params(){return t.page.params}}),N=>a()[1]=N,()=>a()?.[1])}),m(b,C)},$$slots:{default:!0}}),b=>a()[0]=b,()=>a()?.[0])}),m(i,u)},V=i=>{var u=k();const v=w(()=>t.constructors[0]);var g=x(u);A(g,()=>f(v),(l,d)=>{O(d(l,{get data(){return n()},get form(){return t.form},get params(){return t.page.params}}),b=>a()[0]=b,()=>a()?.[0])}),m(i,u)};p(h,i=>{t.constructors[1]?i(I):i(V,!1)})}var j=st(h,2);{var F=i=>{var u=gt(),v=ot(u);{var g=l=>{var d=it();ut(()=>_t(d,f(o))),m(l,d)};p(v,l=>{f(e)&&l(g)})}ct(u),m(i,u)};p(j,i=>{f(r)&&i(F)})}m(c,_),nt()}const wt=ht(yt),At=[()=>E(()=>import("../nodes/0.GHmX8_TY.js"),__vite__mapDeps([0,1,2,3]),import.meta.url),()=>E(()=>import("../nodes/1.CCDFjfsy.js"),__vite__mapDeps([4,1,5,2,6,7,8]),import.meta.url),()=>E(()=>import("../nodes/2.CBmOZecg.js"),__vite__mapDeps([9,1,5,2,6,10,11,8,12,13]),import.meta.url),()=>E(()=>import("../nodes/3.DQDtIKgH.js"),__vite__mapDeps([14,15,1,2,6,10,7,8,11,12,16,5]),import.meta.url)],Ct=[],Dt={"/":[2],"/game/[id]":[3]},Et={handleError:({error:c})=>{console.error(c)},reroute:()=>{},transport:{}},xt=Object.fromEntries(Object.entries(Et.transport).map(([c,t])=>[c,t.decode])),Lt=!1,It=(c,t)=>xt[c](t);export{It as decode,xt as decoders,Dt as dictionary,Lt as hash,Et as hooks,kt as matchers,At as nodes,wt as root,Ct as server_loads};
