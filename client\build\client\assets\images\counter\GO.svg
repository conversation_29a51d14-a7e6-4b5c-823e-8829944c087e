<svg viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient from top to bottom -->
    <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="oklch(78.9% 0.154 211.53)" />
      <stop offset="100%" stop-color="oklch(55.8% 0.288 302.321)" />
    </linearGradient>

    <!-- Outer glow filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="2" result="blur"/>
      <feMerge>
        <feMergeNode in="blur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Shared styles -->
  <style>
    text {
      fill: transparent;
      stroke: url(#gradient);
      stroke-width: 1;
      filter: url(#glow);
      font-family: Arial, sans-serif;
      font-weight: bold;
      text-anchor: middle;
      dominant-baseline: middle;
    }
  </style>

  <!-- Countdown text -->
  <text x="40" y="40" font-size="40">GO</text>
</svg>
