

export const index = 3;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/game/_id_/_page.svelte.js')).default;
export const universal = {
  "ssr": false
};
export const universal_id = "src/routes/game/[id]/+page.ts";
export const imports = ["_app/immutable/nodes/3.DQDtIKgH.js","_app/immutable/chunks/sPbygbFs.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/CBPGQi6i.js","_app/immutable/chunks/D7HYObST.js","_app/immutable/chunks/6c5zsAwx.js","_app/immutable/chunks/XXcWJRIr.js","_app/immutable/chunks/CbN9a2Ga.js","_app/immutable/chunks/CagF9jJf.js","_app/immutable/chunks/Dh9ZwQ9u.js"];
export const stylesheets = ["_app/immutable/assets/MumsNumbers.Cfre7piE.css"];
export const fonts = [];
