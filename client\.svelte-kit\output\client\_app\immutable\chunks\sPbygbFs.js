const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./Dq8x2eD_.js","./CagF9jJf.js","./DsnmJJEf.js","./CBPGQi6i.js","./D7HYObST.js","./6c5zsAwx.js","./CbN9a2Ga.js","../assets/MumsNumbers.Cfre7piE.css","./BZyaMUDL.js","./DmtBy-2V.js","./YY2aR11u.js","./KN2rleQP.js"])))=>i.map(i=>d[i]);
import"./DsnmJJEf.js";import{t as W,x as Ue,y as We,a0 as St,aG as Ct,L as He,aj as wt,al as kt,aq as Tt,ao as _t,av as je,B as xt,aH as Gt,am as ge,p as ne,V as y,aa as L,q as ae,a as Q,g as m,ac as q,b as F,c as oe,aI as it,aJ as Et,d as S,r as b,f as P,s as G,_ as at,h as Ge,i as ve,l as Lt,T as Ft,aK as It,$ as At}from"./CBPGQi6i.js";import{o as Oe,c as nt,s as V,e as be,w as Ot,m as Bt,u as Pt,g as Rt,h as zt}from"./D7HYObST.js";import{i as j,r as Mt,p as U,s as Ee,b as ye,a as ot,_ as Ye}from"./6c5zsAwx.js";import{p as Xe}from"./XXcWJRIr.js";import{A as Dt,H as Nt,I as qe,e as Be,i as Pe,J as Re,s as Se,K as ze,L as Le,N as f,g as w,O as Ut,Q as Ve,V as Wt,W as Ht,X as jt,Y as te,Z as Yt,b as Me,E as he,a as Xt,u as qt,C as le,y as Ce,z as we,t as _e,G as Vt,D as Fe,_ as Kt,$ as Zt,q as Ke,a0 as ue,a1 as Qt,a2 as Ze,a3 as Jt,a4 as ce,a5 as $t,a6 as es}from"./CagF9jJf.js";import{T as ts,t as Qe,u as ss,v as re}from"./BZyaMUDL.js";import{i as rt}from"./Dh9ZwQ9u.js";function is(l,e,t=!1,s=!1,i=!1){var a=l,n="";W(()=>{var c=St;if(n===(n=e()??"")){Ue&&We();return}if(c.nodes_start!==null&&(Ct(c.nodes_start,c.nodes_end),c.nodes_start=c.nodes_end=null),n!==""){if(Ue){He.data;for(var o=We(),d=o;o!==null&&(o.nodeType!==wt||o.data!=="");)d=o,o=kt(o);if(o===null)throw Tt(),_t;je(He,d),a=xt(o);return}var u=n+"";t?u=`<svg>${u}</svg>`:s&&(u=`<math>${u}</math>`);var h=Gt(u);if((t||s)&&(h=ge(h)),je(ge(h),h.lastChild),t||s)for(;ge(h);)a.before(ge(h));else a.before(h)}})}var as=Et("<svg><!></svg>"),ns=P("<span></span>");function ie(l,e){ne(e,!0);const t={name:"",loading:null,destroyed:!1},s=Mt(e,["$$slots","$$events","$$legacy"]);let i=L(!1),a=L(0),n=q(()=>!!e.ssr||m(i)),c=q(()=>(m(a),Nt(e.icon,t,m(n),d,e.onload))),o=q(()=>{const v=m(c)?Dt(m(c).data,s):null;return v&&m(c).classes&&(v.attributes.class=(typeof s.class=="string"?s.class+" ":"")+m(c).classes.join(" ")),v});function d(){it(a)}Oe(()=>{y(i,!0)}),nt(()=>{t.destroyed=!0,t.loading&&(t.loading.abort(),t.loading=null)});var u=ae(),h=Q(u);{var g=v=>{var k=ae(),_=Q(k);{var T=E=>{var I=as();qe(I,()=>({...m(o).attributes}));var z=S(I);is(z,()=>m(o).body,!0),b(I),F(E,I)},C=E=>{var I=ns();qe(I,()=>({...m(o).attributes})),F(E,I)};j(_,E=>{m(o).svg?E(T):E(C,!1)})}F(v,k)};j(h,v=>{m(o)&&v(g)})}F(l,u),oe()}var os=P('<img alt="Player avatar"/>'),rs=P("<div><!></div>"),ls=P('<div class="pointer-events-auto flex flex-col items-start gap-2"><div class="flex items-center gap-2"><!> <div class="flex flex-col"><div class="text-[1.8vh] text-left font-medium leading-tight"> </div> <div class="flex flex-row items-center gap-2"><div class="flex gap-1"></div> <span class="font-bold text-[2vh] align-middle"> </span></div></div></div></div>');function cs(l,e){let t=U(e,"name",3,"You"),s=q(()=>e.compare==="win"?"border-green-400":e.compare==="lose"?"border-red-400":e.compare==="tie"?"border-yellow-400":"border-white/40");var i=ls(),a=S(i),n=S(a);{var c=T=>{var C=os();W(()=>{Re(C,"src",e.avatarUrl),Se(C,1,`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${m(s)??""}`)}),F(T,C)},o=T=>{var C=rs(),E=S(C);ie(E,{icon:"mdi:account",color:"white",height:"3vh"}),b(C),W(()=>Se(C,1,`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${m(s)??""}`)),F(T,C)};j(n,T=>{e.avatarUrl?T(c):T(o,!1)})}var d=G(n,2),u=S(d),h=S(u,!0);b(u);var g=G(u,2),v=S(g);Be(v,21,()=>Array(e.maxLives),Pe,(T,C,E)=>{var I=ae(),z=Q(I);{var Y=O=>{ie(O,{height:"2vh",color:"red",icon:"material-symbols:favorite"})},X=O=>{ie(O,{height:"2vh",color:"red",icon:"material-symbols:favorite-outline"})};j(z,O=>{E<e.lives?O(Y):O(X,!1)})}F(T,I)}),b(v);var k=G(v,2),_=S(k,!0);b(k),b(g),b(d),b(a),b(i),W(()=>{V(h,t()),V(_,e.score)}),F(l,i)}var ds=P('<div class="text-[2vh] italic opacity-80">Waiting for player…</div>'),hs=P('<img alt="Opponent avatar"/>'),ms=P("<div><!></div>"),gs=P('<div class="flex items-center gap-2"><div class="flex flex-col"><div class="text-[1.8vh] text-right font-medium leading-tight"> </div> <div class="flex flex-row items-center gap-2"><span class="font-bold text-[2vh] align-middle"> </span> <div class="flex gap-1"></div></div></div> <!></div>'),us=P('<div class="pointer-events-auto flex items-center gap-2"><div class="flex flex-col items-end gap-1"><!></div></div>');function fs(l,e){let t=U(e,"waiting",3,!0),s=U(e,"score",3,null),i=U(e,"lives",3,null),a=U(e,"name",3,"Opponent"),n=q(()=>e.compare==="win"?"border-green-400":e.compare==="lose"?"border-red-400":e.compare==="tie"?"border-yellow-400":"border-white/40");var c=us(),o=S(c),d=S(o);{var u=g=>{var v=ds();F(g,v)},h=g=>{var v=gs(),k=S(v),_=S(k),T=S(_,!0);b(_);var C=G(_,2),E=S(C),I=S(E,!0);b(E);var z=G(E,2);Be(z,21,()=>Array(e.maxLives),Pe,(M,R,J)=>{var D=ae(),r=Q(D);{var p=A=>{ie(A,{height:"2vh",color:"red",icon:"material-symbols:favorite"})},x=A=>{ie(A,{height:"2vh",color:"red",icon:"material-symbols:favorite-outline"})};j(r,A=>{i()!==null&&J<i()?A(p):A(x,!1)})}F(M,D)}),b(z),b(C),b(k);var Y=G(k,2);{var X=M=>{var R=hs();W(()=>{Re(R,"src",e.avatarUrl),Se(R,1,`w-[5vh] h-[5vh] rounded-full object-cover border-2 ${m(n)??""}`)}),F(M,R)},O=M=>{var R=ms(),J=S(R);ie(J,{icon:"mdi:account",color:"white",height:"3vh"}),b(R),W(()=>Se(R,1,`w-[5vh] h-[5vh] rounded-full bg-gray-600 flex items-center justify-center border-2 ${m(n)??""}`)),F(M,R)};j(Y,M=>{e.avatarUrl?M(X):M(O,!1)})}b(v),W(()=>{V(T,a()),V(I,s())}),F(g,v)};j(d,g=>{t()||s()===null?g(u):g(h,!1)})}b(o),b(c),F(l,c)}var ps=P(`<div class="fixed left-0 right-0 z-10 px-[0vw] py-[0vh] flex flex-col items-center justify-around text-white"><div class="w-full pointer-events-none relative top-0 left-0 right-0 px-[4vw] py-[2vh] flex items-center justify-between"><!> <!></div> <div class="relative w-full flex items-center bg-black/20"><div class="absolute left-1/2 top-1/2 -translate-y-1/2 -translate-1/2 w-[10vh] h-[6vh]
      flex items-center justify-center p-1 rounded-full border-4 border-cyan-400 bg-gray-800 z-10
      font-medium text-[2.5vh]"> </div> <div class="relative w-full h-2 rounded-xl overflow-hidden"><div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-cyan-400 to-purple-600 transition-all duration-1000 ease-linear"></div></div></div></div>`);function vs(l,e){ne(e,!0);let t=U(e,"opponentScore",3,null),s=U(e,"opponentLives",3,null),i=U(e,"opponentWaiting",3,!0);function a(C){const E=Math.floor(C/60),I=Math.floor(C%60);return`${E.toString().padStart(2,"0")}:${I.toString().padStart(2,"0")}`}let n=q(()=>t()==null?null:e.score>t()?"win":e.score<t()?"lose":"tie"),c=q(()=>t()==null?null:t()>e.score?"win":t()<e.score?"lose":"tie");var o=ps(),d=S(o),u=S(d);cs(u,{get score(){return e.score},get lives(){return e.lives},get maxLives(){return e.maxLives},get name(){return e.playerName},get avatarUrl(){return e.playerAvatarUrl},get compare(){return m(n)}});var h=G(u,2);fs(h,{get waiting(){return i()},get score(){return t()},get lives(){return s()},get maxLives(){return e.maxLives},get name(){return e.opponentName},get avatarUrl(){return e.opponentAvatarUrl},get compare(){return m(c)}}),b(d);var g=G(d,2),v=S(g),k=S(v,!0);b(v);var _=G(v,2),T=S(_);b(_),b(g),b(o),W(C=>{V(k,C),ze(T,`width: ${e.time/e.totalTime*100}%;`)},[()=>a(e.time)]),F(l,o),oe()}var bs=P('<div class="fixed inset-0 bg-black/60 flex justify-center items-center z-[2000] svelte-vzg9ol"><img class="counter svelte-vzg9ol" alt=""/></div>');function ys(l,e){ne(e,!0);let t=U(e,"duration",3,3),s=U(e,"show",3,!1),i=L(at(t())),a=L(null);Ge(()=>(s()&&(console.log("Starting countdown"),n()),c));function n(){y(i,t()),y(a,setInterval(()=>{it(i,-1),m(i)<0&&clearInterval(m(a))},1300),!0)}function c(){m(a)&&(clearInterval(m(a)),y(a,null))}var o=ae(),d=Q(o);{var u=h=>{var g=bs(),v=S(g);b(g),W(()=>Re(v,"src",`/assets/images/counter/${(m(i)===0?"GO":m(i))??""}.svg`)),F(h,g)};j(d,h=>{s()&&m(i)>=0&&h(u)})}F(l,o),oe()}var Ss=P('<div class="absolute w-screen h-screen z-1000 flex flex-col justify-center items-center"><div class="background svelte-2ea9pu"></div>  <div class="w-80 h-4 bg-gray-700 rounded-full overflow-hidden"><div class="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full transition-all duration-300 ease-out"></div></div> <p class="text-sm text-gray-300 mt-4"> </p></div>');function Cs(l,e){let t=q(()=>e.progress>=1);var s=ae(),i=Q(s);{var a=n=>{var c=Ss(),o=G(S(c),2),d=S(o);b(o);var u=G(o,2),h=S(u);b(u),b(c),W((g,v)=>{ze(d,`width: ${g??""}%`),V(h,`${v??""}%`)},[()=>Math.max(0,Math.min(100,e.progress*100)),()=>Math.round(Math.max(0,Math.min(100,e.progress*100)))]),F(n,c)};j(i,n=>{m(t)||n(a)})}F(l,s)}var ws=P('<div class="error-type-badge svelte-jbysz9"> </div>'),ks=P('<div class="modal-backdrop svelte-jbysz9" role="dialog" aria-modal="true"><div class="modal-container svelte-jbysz9"><div class="modal-blur-bg svelte-jbysz9"></div> <div class="modal-content svelte-jbysz9"><div class="error-icon svelte-jbysz9"><svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="svelte-jbysz9"><circle cx="12" cy="12" r="10" stroke="#ff4444" stroke-width="2" fill="none" class="svelte-jbysz9"></circle><path d="M15 9l-6 6" stroke="#ff4444" stroke-width="2" stroke-linecap="round" class="svelte-jbysz9"></path><path d="M9 9l6 6" stroke="#ff4444" stroke-width="2" stroke-linecap="round" class="svelte-jbysz9"></path></svg></div> <div class="error-title svelte-jbysz9"><h2 class="svelte-jbysz9">Game Error</h2></div> <!> <div class="error-message svelte-jbysz9"><p class="svelte-jbysz9"> </p></div> <div class="modal-actions svelte-jbysz9"><button class="close-btn svelte-jbysz9"><span class="svelte-jbysz9">Close</span></button></div></div></div></div>');function Ts(l,e){ne(e,!1);let t=U(e,"isVisible",8,!1),s=U(e,"errorMessage",8,""),i=U(e,"errorType",8,""),a=U(e,"onClose",8,()=>{});function n(){a()()}function c(h){h.target===h.currentTarget&&n()}rt();var o=ae(),d=Q(o);{var u=h=>{var g=ks(),v=S(g),k=G(S(v),2),_=G(S(k),4);{var T=X=>{var O=ws(),M=S(O,!0);b(O),W(R=>V(M,R),[()=>(Lt(i()),ve(()=>i().toUpperCase()))]),F(X,O)};j(_,X=>{i()&&X(T)})}var C=G(_,2),E=S(C),I=S(E,!0);b(E),b(C);var z=G(C,2),Y=S(z);b(z),b(k),b(v),b(g),W(()=>V(I,s())),be("click",Y,n),be("click",g,c),F(h,g)};j(d,h=>{t()&&h(u)})}F(l,o),oe()}var _s=P('<div class="row svelte-fpngv5"><div class="rank svelte-fpngv5"></div> <div class="userid svelte-fpngv5"> </div> <div class="score svelte-fpngv5"> </div> <div class="status svelte-fpngv5"> </div></div>'),xs=P('<div id="popup-leaderboard" class="overlay svelte-fpngv5"><div class="modal svelte-fpngv5"><div class="header svelte-fpngv5"><div class="title svelte-fpngv5">Room Leaderboard</div> <button class="close-btn svelte-fpngv5">×</button></div> <div class="list svelte-fpngv5"></div></div></div>');function Gs(l,e){ne(e,!1);const[t,s]=ot(),i=()=>Ee(o,"$leaderboard",t);let a=U(e,"roomId",8),n=U(e,"open",12,!1),c=Ft(void 0);const o=Ot([]);function d(T){!T||T.roomId!==a()||o.set(T.leaderboard)}function u(){n(!n()),m(c)&&It(c,m(c).style.display=n()?"block":"none")}Oe(()=>{const T=C=>d(C);return Le.addCustomEventListener("leaderboard_update",T),()=>{Le.removeCustomEventListener("leaderboard_update",T)}}),rt();var h=xs(),g=S(h),v=S(g),k=G(S(v),2);b(v);var _=G(v,2);Be(_,5,i,Pe,(T,C,E)=>{var I=_s(),z=S(I);z.textContent=E+1;var Y=G(z,2),X=S(Y,!0);b(Y);var O=G(Y,2),M=S(O,!0);b(O);var R=G(O,2),J=S(R,!0);b(R),b(I),W(()=>{V(X,(m(C),ve(()=>m(C).name??m(C).userId))),V(M,(m(C),ve(()=>m(C).score))),V(J,(m(C),ve(()=>m(C).status)))}),F(T,I)}),b(_),b(g),b(h),ye(h,T=>y(c,T),()=>m(c)),W(()=>ze(h,`display: ${n()?"block":"none"}`)),be("click",k,u),F(l,h),oe(),s()}var Es=P('<button class="fixed bottom-4 right-4 z-[3500] rounded-full bg-[#151a22] border border-[#222a36] text-white shadow-lg p-3" aria-label="Toggle Leaderboard"><!></button>');function Ls(l,e){let t=U(e,"onToggle",8);var s=Es(),i=S(s);ie(i,{icon:"mdi:trophy",height:"24"}),b(s),be("click",s,function(...a){t()?.apply(this,a)}),F(l,s)}let Fs=class extends f.Scene{constructor(){super({key:"PreloadScene"})}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("block_active","/assets-finger-frenzy/images/block_active.png"),this.load.image("block_inactive","/assets-finger-frenzy/images/block_inactive.png"),this.load.image("game_name","/assets-finger-frenzy/images/game_name.png"),this.load.image("game_start","/assets/images/game_start.png"),this.load.image("timer_icon","/assets/images/timer_icon.png"),this.load.image("countdown-3","/assets/images/countdown-3.png"),this.load.image("countdown-2","/assets/images/countdown-2.png"),this.load.image("countdown-1","/assets/images/countdown-1.png"),this.load.image("countdown-go","/assets/images/countdown-go.png"),this.load.image("back_to_lobby","/assets/images/back_to_lobby.png"),this.load.image("game_bg","/assets/images/game_bg.png"),this.load.svg("heart","/assets/images/mdi--heart.svg"),this.load.svg("heart_outline","/assets/images/mdi-light--heart.svg"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.svg("button_bg","/assets/images/button_bg.svg"),this.load.svg("game_over","/assets/images/game_over.svg"),this.load.svg("timer_bg","/assets/images/timer_bg.svg"),this.load.image("timer_countdown_bg","/assets/images/timer_countdown_bg.png"),this.load.audio("click",["/assets/audio/click.ogg","/assets/audio/click.mp3","/assets/audio/click.wav"]),this.load.audio("wrong",["/assets/audio/wrong.ogg","/assets/audio/wrong.mp3","/assets/audio/wrong.wav"]),this.load.audio("countdown",["/assets/audio/countdown.ogg","/assets/audio/countdown.mp3","/assets/audio/countdown.wav"]),this.load.audio("go",["/assets/audio/go.mp3","/assets/audio/go.wav"]),this.load.audio("tap",["/assets-finger-frenzy/sounds/tap.ogg","/assets-finger-frenzy/sounds/tap.mp3","/assets-finger-frenzy/sounds/tap.wav"]),this.load.audio("right",["/assets-finger-frenzy/sounds/right.ogg","/assets-finger-frenzy/sounds/right.mp3","/assets-finger-frenzy/sounds/right.wav"]),this.load.audio("timeout",["/assets-finger-frenzy/sounds/timeout.ogg","/assets-finger-frenzy/sounds/timeout.mp3","/assets-finger-frenzy/sounds/timeout.wav"])}create(){}},ke=class{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}hasNotifiedReady=!1;notifyGameReady(){console.log("notifyGameReady -- ENTER"),!this.hasNotifiedReady&&(this.hasNotifiedReady=!0,console.log("Notifying game ready"),this.sendMessage({type:"gameReady"}))}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}},Is=class extends f.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new ke,this.add.image(0,0,"game_bg").setOrigin(0,0).setDisplaySize(e,t);const s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8);s.setScale(i),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"}),this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5);const a=Math.min(e*.6/this.startButton.width,.4);this.startButton.setScale(a);const n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5),c=this.startButton.displayWidth*.6/n.width;n.setScale(c),this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.ticTaps.notifyGameReady(),this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:100,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:.7}),this.tweens.add({targets:s,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameScene")}})}})}},lt=class{scene;config;score;scoreText;scoreLabel;container;events;constructor(e,t={}){this.scene=e,this.events=new f.Events.EventEmitter,this.config={initialScore:t.initialScore??0,fontFamily:t.fontFamily??"Arial",fontSize:t.fontSize??"80px",labelFontSize:t.labelFontSize??"28px",scoreColor:t.scoreColor??"#33DDFF",labelColor:t.labelColor??"#FFFFFF",animationColor:t.animationColor??"#ffff00",animationDuration:t.animationDuration??800},this.score=this.config.initialScore}createUI(e,t,s){this.container=this.scene.add.container(0,0),this.scoreLabel=this.scene.add.text(e,t-30,"Total Point",{fontFamily:this.config.fontFamily,fontSize:this.config.labelFontSize,fontStyle:"bold",color:this.config.labelColor}).setOrigin(.5),this.scoreText=this.scene.add.text(e,t+30,this.score.toString(),{fontFamily:this.config.fontFamily,fontSize:this.config.fontSize,fontStyle:"bold",color:this.config.scoreColor}).setOrigin(.5),this.container.add([this.scoreLabel,this.scoreText]),s&&s.add(this.container)}addPoints(e,t){this.score+=e,this.updateScoreDisplay(),t&&this.createScoreAnimation(t),w.updateScore(this.score),this.events.emit("scoreChanged",this.score,e)}subtractPoints(e,t){this.score=Math.max(0,this.score-e),this.updateScoreDisplay(),t&&this.createScoreAnimation(t),w.updateScore(this.score),this.events.emit("scoreChanged",this.score,-e)}setScore(e){const t=this.score;this.score=e,this.updateScoreDisplay(),this.events.emit("scoreChanged",this.score,this.score-t)}getScore(){return this.score}reset(){this.score=this.config.initialScore,this.updateScoreDisplay(),this.events.emit("scoreReset",this.score)}updateScoreDisplay(){this.scoreText&&this.scoreText.setText(this.score.toString())}createScoreAnimation(e){const t=this.scene.add.text(e.startX,e.startY,`+${e.points}`,{fontFamily:this.config.fontFamily,fontSize:"24px",color:e.color??this.config.animationColor,stroke:"#000000",strokeThickness:3});t.setOrigin(.5),this.scene.tweens.add({targets:t,y:e.startY-50,alpha:0,scale:1.2,duration:e.duration??this.config.animationDuration,ease:"Power2",onComplete:()=>{t.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.scoreText=void 0,this.scoreLabel=void 0,this.container=void 0}},De=class{scene;hearts=[];container;events;constructor(e){this.scene=e,this.events=new f.Events.EventEmitter}deductHeart(e,t,s){e!==void 0&&t!==void 0&&(s||this.container)?(console.log("deducting heart"),this.createFlyingHeartAnimation(e,t,s)):console.error("No coordinates provided for LivesManager",e,t,s)}createFlyingHeartAnimation(e,t,s){if(!this.container&&!s){console.error("No container found for LivesManager");return}const i=this.scene.add.image(e,t,"heart_broken").setOrigin(.5).setScale(1.5).setAlpha(.4);this.scene.tweens.add({targets:i,y:t-200,scale:3,alpha:.8,duration:600,ease:"Power2",onComplete:()=>{i.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.container=void 0}},As=class extends f.Scene{blocks=[];gameEnd=!1;gridContainer;scoreManager;livesManager;ticTaps;socketClient=null;roomId="room-"+Date.now().toString(36);gameId="finger-frenzy";constructor(){super({key:"GameScene"}),this.ticTaps=new ke}init(){this.gameEnd=!1;const e=this.registry.get("gameConfig");this.socketClient=e?.socketClient||null,this.roomId=e?.roomId||"room-"+Date.now().toString(36),this.gameId=e?.gameId||"finger-frenzy",this.setupSocketEventListeners(),this.scoreManager=new lt(this,{initialScore:0,fontSize:"80px",scoreColor:"#33DDFF"}),this.livesManager=new De(this)}create(){this.createBackground(),this.createGrid(),this.initializeGame()}shutdown(){this.tweens.killAll(),this.cleanupSocketEventListeners(),this.scoreManager&&this.scoreManager.destroy(),this.livesManager&&this.livesManager.destroy()}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for FingerFrenzy GameScene"))}setupSocketEventListeners(){this.socketClient&&(this.socketClient.addCustomEventListener("initialized",e=>{console.log("Game initialized by server:",e),this.startCountdown()}),this.socketClient.addCustomEventListener("started",e=>{console.log("Game started by server:",e),this.startGame()}),this.socketClient.addCustomEventListener("action_result",e=>{console.log("Action result from server:",e),e.actionType==="tile_tap"&&this.handleTileTapResult(e.data)}),this.socketClient.addCustomEventListener("game_error",e=>{console.error("Game error from server:",e)}),this.socketClient.addCustomEventListener("game_fatal_error",e=>{console.error("Fatal game error from server:",e),this.handleFatalError(e.message,e.errorType)}),console.log("Socket event listeners setup for FingerFrenzy GameScene"))}createBackground(){const{width:e,height:t}=this.cameras.main,s=this.textures.createCanvas("bgTexture",e,t),i=s?.getContext();if(i&&s){const a=i.createLinearGradient(0,0,0,t);a.addColorStop(0,"#212429"),a.addColorStop(1,"#1C1D22"),i.fillStyle=a,i.fillRect(0,0,e,t),s.refresh(),this.add.image(e/2,t/2,"bgTexture").setOrigin(.5)}else this.cameras.main.setBackgroundColor("#1C1D22")}initializeGame(){console.log("Initializing Finger Frenzy game..."),w.initGame(),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.gameEnd=!0,this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()&&this.socketClient.startGame()}createGrid(){const{width:e,height:t}=this.cameras.main,s=Math.min(e*.9,550),i=s*1.2;this.gridContainer=this.add.container(e/2,t*.6);const a=this.add.graphics();a.fillStyle(1710618,1),a.fillRoundedRect(-s/2,-i/2,s,i,20),a.lineStyle(4,4553205,1),a.strokeRoundedRect(-s/2,-i/2,s,i,20),this.gridContainer.add(a),this.blocks=[];const n=s*.03,c=(s-n*5)/4,o=(i-n*5)/4,d=-s/2+n,u=-i/2+n;for(let h=0;h<4;h++)for(let g=0;g<4;g++){const v=d+g*(c+n)+c/2,k=u+h*(o+n)+o/2,_=new Ut(this,v,k,c,o,h,g);this.blocks.push(_),this.gridContainer.add(_),_.on("pointerdown",T=>this.onBlockClick(_,T))}}onBlockClick(e,t){if(this.gameEnd||!e||t.getDuration()>100)return;e.disableInteractive();const i=Date.now()-e.getActivationTime();if(console.log("reactionTime",i),this.socketClient&&this.socketClient.isConnected()){const a=this.getTileId(e);this.socketClient.sendTileTap(a,i)}}activateRandomBlock(e){if(this.gameEnd||this.blocks.filter(i=>i.getBlockActive()).length>=Ve.INITIAL_ACTIVE_BLOCKS)return;const s=[];if(this.blocks.forEach((i,a)=>{!i.getBlockActive()&&!(e!==void 0&&a===e)&&s.push(a)}),s.length>0){const i=f.Utils.Array.GetRandom(s);this.blocks[i].setBlockActive(!0)}}startGame(){this.blocks.forEach(t=>t.reset());const e=[{row:1,col:2},{row:2,col:3},{row:0,col:1}];for(const t of e){const s=t.row*4+t.col;s<this.blocks.length&&this.blocks[s].setBlockActive(!0)}console.log("Game started with 3 active blocks")}endGame(){this.gameEnd||(this.gameEnd=!0,w.endGame(),this.sound.play("timeout"),this.blocks.forEach(e=>e.disableInteractive()))}quitGame(){this.endGame(),this.socketClient&&this.socketClient.isConnected()&&this.socketClient.endGame("manual")}getTileId(e){const t=this.blocks.indexOf(e),s=Math.floor(t/4),i=t%4;return`block_${s}_${i}`}handleTileTapResult(e){console.log("Handling tile tap result:",e);const t=this.blocks.find(s=>s.getTileId()===e.tileId);if(!t){console.warn("Block not found for tile ID:",e.tileId);return}console.log("Block found:",t.getTileId()),e.isCorrect?(this.sound.play("right"),t.setBlockActive(!1),this.scoreManager.addPoints(e.points,{startX:this.gridContainer.x+t.x,startY:this.gridContainer.y+t.y,color:"#ffff00",points:e.points})):(this.sound.play("wrong"),t.setBlockWrong(),w.updateLives(e.newLives),this.scoreManager.subtractPoints(e.points,{startX:this.gridContainer.x+t.x,startY:this.gridContainer.y+t.y,color:"#ff0000",points:e.points}),this.livesManager.deductHeart(this.gridContainer.x+t.x,this.gridContainer.y+t.y,this.gridContainer)),this.time.delayedCall(Ve.COOLDOWN_DURATION*1e3,()=>{t.setInteractive({useHandCursor:!0})}),e.gameState&&w.updateScore(e.gameState.score),e.gridState&&this.syncGridState(e.gridState),e.gameEnded&&this.endGame()}syncGridState(e){!e||!e.blocks||e.blocks.forEach(t=>{const s=t.index;s>=0&&s<this.blocks.length&&this.blocks[s].setBlockActive(t.isActive)})}},Os=class extends f.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene"),this.ticTaps=new ke}init(e){this.score=e.score||0,console.log("GameEndScene init - Score:",this.score)}create(){this.ticTaps=new ke,this.add.image(0,0,"game_bg").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===f.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===f.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,Bs(o,2,2,e,t,18),a.refresh();const u=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(u)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const c=new f.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(c,f.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const c="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(c)&&this.textures.remove(c);const o=Math.max(400,s.length*i*.7),d=i*1.5,u=this.textures.createCanvas(c,o,d);if(!u)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=u.getContext(),g=h.createLinearGradient(0,0,o,d*.5);return a?(g.addColorStop(0,"#4cffae"),g.addColorStop(.4,"#32c4ff"),g.addColorStop(1,"#5c67ff")):n?(g.addColorStop(0,"#32c4ff"),g.addColorStop(.5,"#7f54ff"),g.addColorStop(1,"#b63efc")):(g.addColorStop(0,"#33DDFF"),g.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=g,h.fillText(s,o/2,d/2),u.refresh(),this.add.image(e,t,c).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}};function Bs(l,e,t,s,i,a,n,c){l.beginPath(),l.moveTo(e+a,t),l.lineTo(e+s-a,t),l.quadraticCurveTo(e+s,t,e+s,t+a),l.lineTo(e+s,t+i-a),l.quadraticCurveTo(e+s,t+i,e+s-a,t+i),l.lineTo(e+a,t+i),l.quadraticCurveTo(e,t+i,e,t+i-a),l.lineTo(e,t+a),l.quadraticCurveTo(e,t,e+a,t),l.closePath(),l.stroke()}class Ps{config;gameInstance=null;constructor(e){this.config=e}async init(){console.log("Initializing FingerFrenzy game...");const e={type:f.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:this.config.containerId,scene:[Fs,Is,As,Os],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:f.Scale.EXPAND,autoCenter:f.Scale.CENTER_BOTH},render:{antialias:!0,pixelArt:!1,roundPixels:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new f.Game(e),this.gameInstance&&this.gameInstance.registry.set("gameConfig",this.config)}start(){console.log("Starting FingerFrenzy game..."),this.gameInstance?.scene.start("GameScene")}pause(){console.log("Pausing FingerFrenzy game...")}resume(){console.log("Resuming FingerFrenzy game...")}destroy(){console.log("Destroying FingerFrenzy game..."),this.gameInstance&&(this.gameInstance=null)}getCurrentScore(){return 0}}let Rs=class extends f.Scene{constructor(){super("PreloadScene")}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("game_background","/assets/images/game_bg.png"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.audio("click",["/assets/audio/click.mp3","/assets/audio/click.ogg"]),this.load.audio("wrong",["/assets/audio/wrong.ogg","/assets/audio/wrong.mp3","/assets/audio/wrong.wav"]),this.load.audio("countdown",["/assets/audio/countdown.mp3","/assets/audio/countdown.ogg"]),this.load.audio("go",["/assets/audio/go.mp3","/assets/audio/go.wav"]),this.load.audio("match",["/assets-bingo/audio/match.mp3","/assets-bingo/audio/match.ogg"]),this.load.audio("win",["/assets-bingo/audio/win.mp3","/assets-bingo/audio/win.ogg"]),this.load.audio("number-appear",["/assets-bingo/audio/number_appear.mp3","/assets-bingo/audio/number_appear.ogg"])}create(){}},ct=class{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}notifyGameReady(){this.sendMessage({type:"gameReady"})}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}},zs=class extends f.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new ct,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(e,t);const s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8);s.setScale(i),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"}),this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5);const a=Math.min(e*.6/this.startButton.width,.4);this.startButton.setScale(a);const n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5),c=this.startButton.displayWidth*.6/n.width;n.setScale(c),this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.ticTaps.notifyGameReady(),this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:100,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:.7}),this.tweens.add({targets:s,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameScene")}})}})}};const Ms={COLUMNS:["B","I","N","G","O"]},Ds={horizontal:"LINE BINGO!",vertical:"LINE BINGO!",diagonal:"DIAGONAL BINGO!",fullCard:"FULL CARD!"};let Ns=class dt extends f.Scene{static BINGO_COLUMNS=Ms.COLUMNS;gameEnd;UIContainer;socketClient=null;gameId="bingo";roomId="room-"+Date.now().toString(36);bingoCard;bgoCells;rightNumbers;rightPositions;countdownPanel;countdownText;scoreManager;livesManager;cameraWidth;cameraHeight;centerX;centerY;constructor(){super({key:"GameScene"})}init(){const e=this.registry.get("gameConfig");this.socketClient=e?.socketClient||null,this.gameId=e?.gameId||"bingo",this.roomId=e?.roomId||"room-"+Date.now().toString(36),this.UIContainer=this.add.container(0,0),this.socketClient&&this.setupSocketEventListeners(),this.scoreManager=new lt(this,{initialScore:0,fontSize:"80px",scoreColor:"#33DDFF"}),this.livesManager=new De(this)}setupSocketEventListeners(){this.socketClient&&(this.socketClient.addCustomEventListener("initialized",e=>{console.log("Bingo game initialized by server:",e),console.log("Received bingoGameState:",e.bingoGameState),e.bingoGameState?(console.log("Syncing bingo card from server:",e.bingoGameState.bingoCard),this.syncGameStateFromServer(e.bingoGameState)):console.error("No bingoGameState received from server!"),this.startCountdown()}),this.socketClient.addCustomEventListener("started",e=>{console.log("Bingo game started by server:",e),this.startGame()}),this.socketClient.addCustomEventListener("action_result",e=>{console.log("Action result from server:",e),e.actionType==="cell_mark"&&this.handleCellMarkResult(e.data)}),this.socketClient.addCustomEventListener("number_called",e=>{console.log("Number called by server:",e),this.handleNumberCalled(e.calledNumber,e.gameState)}),this.socketClient.addCustomEventListener("ended",e=>{console.log("Game ended by server:",e),this.endGame()}),this.socketClient.addCustomEventListener("game_error",e=>{console.error("Game error from server:",e)}),this.socketClient.addCustomEventListener("game_fatal_error",e=>{console.error("Fatal game error from server:",e),this.handleFatalError(e.message,e.errorType)}),console.log("Socket event listeners setup for Bingo GameScene"))}syncGameStateFromServer(e){console.log("Syncing game state from server:",e),e.bingoCard?(console.log("Setting bingo card from server:",e.bingoCard),this.bingoCard=e.bingoCard):console.warn("No bingoCard in server game state")}handleCellMarkResult(e){const{cellId:t,isCorrect:s,points:i,newScore:a,newLives:n,gameEnded:c,winResult:o,gameState:d,matchedCalledNumber:u}=e;d&&this.syncGameStateFromServer(d);const h=this.findBingoCellById(t);if(h&&s){if(this.scoreManager.addPoints(i,{startX:h.x,startY:h.y,points:i}),h.mark(),this.sound.play("match"),u){const g=`${u.column}${u.number}`,v=this.rightNumbers.find(k=>k.name===g);v&&(this.children.list.forEach(k=>{k.type==="Graphics"&&k.x===h.x-40&&k.y===h.y-40&&k.destroy()}),this.rightNumbers=this.rightNumbers.filter(k=>k!==v),v.destroy())}}else s||(this.sound.play("wrong"),w.updateLives(e.newLives),h&&this.UIContainer&&this.livesManager.deductHeart(h.x,h.y,this.UIContainer));c&&(o?.hasWon?this.handleBingoWin(o):this.endGame())}handleNumberCalled(e,t){this.syncGameStateFromServer(t),this.addRightItemFromServer(e)}addRightItemFromServer(e){if(this.gameEnd)return;this.sound.play("number-appear"),this.moveExistingRightItems();const t=this.getRightPosition(4),s=new Wt(this,t.x,t.y,4,e.column,e.number);this.rightNumbers.push(s)}create(){this.cameraWidth=this.cameras.main.width,this.cameraHeight=this.cameras.main.height,this.centerX=this.cameraWidth/2,this.centerY=this.cameraHeight/2,this.gameEnd=!1,this.bgoCells=[],this.rightNumbers=[],this.rightPositions=this.calculateRightPositions(),this.createBackground(),this.initializeGame()}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("number_called",()=>{}),this.socketClient.removeCustomEventListener("ended",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for Bingo GameScene"))}shutdown(){this.cleanupSocketEventListeners(),this.scoreManager&&this.scoreManager.destroy()}createBackground(){this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameraWidth,this.cameraHeight)}createCountdownOverlay(){this.countdownPanel=this.add.container(0,0),this.countdownPanel.setDepth(2);const e=this.add.rectangle(0,0,this.cameraWidth,this.cameraHeight,0,.7).setOrigin(0,0);this.countdownPanel.add(e),this.countdownText=this.add.image(this.centerX,this.centerY,"countdown-3").setScale(0).setOrigin(.5),this.countdownPanel.add(this.countdownText)}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}initializeGame(){console.log("Initializing Bingo game..."),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()?this.socketClient.startGame():console.error("No server connection available - cannot start Bingo game")}startGame(){if(!this.bingoCard||this.bingoCard.length===0){console.error("No bingo card data received from server!");return}console.log("Starting game with server-provided bingo card:",this.bingoCard),this.createBingoBoard()}createBingoBoard(){if(console.log("Creating bingo board with server data:",this.bingoCard),!this.bingoCard||this.bingoCard.length===0){console.error("Cannot create bingo board: no server data available");return}const e=80,t=10,s=(e+t)*5-t,i=this.centerX-s/2,n=this.centerY+150-(e+t)*2.5,c=n+(e+t);for(let o=0;o<5;o++){const d=i+o*(e+t)+e/2,u=n,h=this.add.graphics();h.fillGradientStyle(3172095,4674303,6180351,2187007,1),h.lineStyle(3,58798,1),h.fillRoundedRect(d-e/2,u-e/2,e,e,16),h.strokeRoundedRect(d-e/2,u-e/2,e,e,16),this.add.text(d,u,dt.BINGO_COLUMNS[o],{fontFamily:'"TT Neoris", Arial, sans-serif',fontSize:"48px",color:"#FFFFFF",align:"center",fontStyle:"bold",stroke:"#000000",strokeThickness:3,shadow:{offsetX:2,offsetY:2,color:"#000000",blur:2,fill:!0}}).setOrigin(.5)}for(let o=0;o<5;o++)for(let d=0;d<5;d++){const u=i+d*(e+t)+e/2,h=c+o*(e+t),g=this.bingoCard[o][d],v=new Ht(this,u,h,g.column,g.isFree?0:g.number,g.isFree);g.isFree||(v.letterText.alpha=0),v.setScale(0),this.bgoCells.push(v),this.time.delayedCall(150*o+50*d,()=>{this.tweens.add({targets:v,scale:1,duration:300,ease:"Back.Out"})})}}calculateRightPositions(){const e=[],a=this.centerX-220,n=this.centerY-180;for(let c=0;c<5;c++)e.push({x:a+c*90+80/2,y:n});return e}getRightPosition(e){return e>=0&&e<this.rightPositions.length?this.rightPositions[e]:{x:this.centerX+100,y:this.centerY}}moveExistingRightItems(){for(const e of this.rightNumbers)e.moveToPosition(e.rightIndex-1);this.rightNumbers=this.rightNumbers.filter(e=>e.rightIndex>=0)}checkForMatch(e){if(!this.gameEnd){if(this.socketClient&&this.socketClient.isConnected()){this.socketClient.sendGameAction("cell_mark",{cellId:e.name});return}this.checkForMatchLocal(e)}}checkForMatchLocal(e){const t=this.rightNumbers.find(s=>s.name===e.name);t?(e.mark(),this.children.list.forEach(s=>{s.type==="Graphics"&&s.x===e.x-40&&s.y===e.y-40&&s.destroy()}),this.sound.play("match"),this.rightNumbers=this.rightNumbers.filter(s=>s!==t),t.destroy()):this.sound.play("wrong")}handleBingoWin(e){this.gameEnd||(this.gameEnd=!0,this.endGame(),this.createWinAnnouncement(e),this.createCelebrationEffects())}createWinAnnouncement(e){const t=this.add.text(this.centerX,this.centerY-100,Ds[e.pattern],{fontFamily:'"TT Neoris", Arial, sans-serif',fontSize:"64px",color:"#FFD700",align:"center",fontStyle:"bold",stroke:"#000000",strokeThickness:4,shadow:{offsetX:3,offsetY:3,color:"#000000",blur:5,fill:!0}}).setOrigin(.5).setDepth(10);t.setScale(0),this.tweens.add({targets:t,scale:1,duration:500,ease:"Back.Out",onComplete:()=>{this.tweens.add({targets:t,scale:{from:1,to:1.1},duration:800,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"})}})}findBingoCellById(e){return this.bgoCells.find(t=>t.name===e)||null}findBingoCellByName(e){return this.bgoCells.find(t=>t.name===e)||null}endGame(){this.gameEnd||(this.gameEnd=!0,w.endGame(),this.sound.play("timeout"),this.createCelebrationEffects())}createCelebrationEffects(){this.sound.play("win");for(const e of this.bgoCells)e.marked||e.mark(),this.time.delayedCall(Math.random()*1e3,()=>{e.createWinParticles()})}},Us=class extends f.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene")}init(e){this.score=e.score||0}create(){this.ticTaps=new ct,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===f.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===f.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,Ws(o,2,2,e,t,18),a.refresh();const u=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(u)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const c=new f.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(c,f.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const c="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(c)&&this.textures.remove(c);const o=Math.max(400,s.length*i*.7),d=i*1.5,u=this.textures.createCanvas(c,o,d);if(!u)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=u.getContext(),g=h.createLinearGradient(0,0,o,d*.5);return a?(g.addColorStop(0,"#4cffae"),g.addColorStop(.4,"#32c4ff"),g.addColorStop(1,"#5c67ff")):n?(g.addColorStop(0,"#32c4ff"),g.addColorStop(.5,"#7f54ff"),g.addColorStop(1,"#b63efc")):(g.addColorStop(0,"#33DDFF"),g.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=g,h.fillText(s,o/2,d/2),u.refresh(),this.add.image(e,t,c).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}};function Ws(l,e,t,s,i,a,n,c){l.beginPath(),l.moveTo(e+a,t),l.lineTo(e+s-a,t),l.quadraticCurveTo(e+s,t,e+s,t+a),l.lineTo(e+s,t+i-a),l.quadraticCurveTo(e+s,t+i,e+s-a,t+i),l.lineTo(e+a,t+i),l.quadraticCurveTo(e,t+i,e,t+i-a),l.lineTo(e,t+a),l.quadraticCurveTo(e,t,e+a,t),l.closePath(),l.stroke()}class Hs{config;gameInstance=null;constructor(e){this.config=e;const t={type:f.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:e.containerId,scene:[Rs,zs,Ns,Us],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:f.Scale.EXPAND,autoCenter:f.Scale.CENTER_BOTH},render:{antialias:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new f.Game(t),this.gameInstance.registry.set("gameConfig",this.config)}async init(){console.log("Initializing Bingo game...")}start(){console.log("Starting Bingo game..."),this.gameInstance?.scene.start("GameScene")}pause(){console.log("Pausing Bingo game...")}resume(){console.log("Resuming Bingo game...")}destroy(){console.log("Destroying Bingo game..."),this.gameInstance&&(this.gameInstance.destroy(!0),this.gameInstance=null)}getCurrentScore(){return 0}}const Z={BACKGROUND_COLOR:"#0E0F1E",CARD_SIZE:150,ANIMAL_COUNT:4,COLOR_COUNT:3,OPTION_CARDS_COUNT:5},js={0:6750207,1:6750054,2:16777062};let Ys=class extends f.Scene{animalImages=[];constructor(){super("PreloadScene")}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("card_bg","/assets-matching-mayhem/images/card_bg.svg"),this.load.image("card_correct_bg","/assets-matching-mayhem/images/card_correct_bg.png"),this.load.image("card_incorrect_bg","/assets-matching-mayhem/images/card_incorrect_bg.png"),this.load.image("timer_bg","/assets/images/timer_bg.svg"),this.load.image("timer_icon","/assets/images/timer_icon.png"),this.load.image("timer_countdown_bg","/assets/images/timer_countdown_bg.png"),this.load.svg("heart","/assets/images/mdi--heart.svg"),this.load.svg("heart_outline","/assets/images/mdi-light--heart.svg"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.image("game_name","/assets-matching-mayhem/images/game_name.svg"),this.load.image("game_background","/assets/images/game_bg.png"),this.load.image("button_bg","/assets/images/button_bg.svg"),this.load.image("game_start","/assets/images/game_start.png"),this.load.image("game_over","/assets/images/game_over.svg"),this.load.image("back_to_lobby","/assets/images/back_to_lobby.png");for(let e=0;e<Z.ANIMAL_COUNT;e++)this.animalImages[e]=[];for(let e=0;e<Z.ANIMAL_COUNT;e++)for(let t=0;t<Z.COLOR_COUNT;t++){const s=`image_${t}_${e}`;this.load.image(s,`/assets-matching-mayhem/images/${t}/${e}.png`),this.animalImages[e][t]=s}this.load.audio("countdown",["/assets/audio/countdown.mp3","/assets/audio/countdown.wav"]),this.load.audio("go",["/assets/audio/go.mp3","/assets/audio/go.wav"]),this.load.audio("wrong",["/assets/audio/wrong.mp3","/assets/audio/wrong.wav"]),this.load.audio("end",["/assets-matching-mayhem/sounds/end.mp3","/assets-matching-mayhem/sounds/end.wav"]),this.load.audio("correct",["/assets-matching-mayhem/sounds/correct.mp3","/assets-matching-mayhem/sounds/correct.wav"]),this.load.audio("round",["/assets-matching-mayhem/sounds/round.mp3","/assets-matching-mayhem/sounds/round.wav"]),this.load.audio("laser",["/assets-matching-mayhem/sounds/laser.mp3","/assets-matching-mayhem/sounds/laser.wav"]),this.load.image("countdown-3","/assets/images/countdown-3.png"),this.load.image("countdown-2","/assets/images/countdown-2.png"),this.load.image("countdown-1","/assets/images/countdown-1.png"),this.load.image("countdown-go","/assets/images/countdown-go.png"),this.load.bitmapFont("game_font","/assets-matching-mayhem/fonts/font.png","/assets-matching-mayhem/fonts/font.xml")}getAnimalImageKey(e,t){return e>=0&&e<this.animalImages.length&&t>=0&&t<this.animalImages[e].length?this.animalImages[e][t]:(console.error(`Invalid animal or color index: ${e}, ${t}`),"")}create(){this.game.registry.set("animalImages",this.animalImages)}},ht=class{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}notifyGameReady(){this.sendMessage({type:"gameReady"})}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}},Xs=class extends f.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new ht,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(e,t);const s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8);s.setScale(i),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"}),this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5);const a=Math.min(e*.6/this.startButton.width,.4);this.startButton.setScale(a);const n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5),c=this.startButton.displayWidth*.6/n.width;n.setScale(c),this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.ticTaps.notifyGameReady(),this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:100,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:.7}),this.tweens.add({targets:s,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameScene")}})}})}};class qs{scene;config;container;backgroundGraphics;progressGraphics;maskGraphics;constructor(e,t){this.scene=e,this.config={x:t.x,y:t.y,size:t.size,cornerRadius:t.cornerRadius??16,backgroundColor:t.backgroundColor??3355443,backgroundAlpha:t.backgroundAlpha??.6,borderWidth:t.borderWidth??8,startColor:t.startColor??"#ff4d4d",endColor:t.endColor??"#33ff55"}}create(e){this.container=this.scene.add.container(0,0),this.createBackground(),this.createProgress(),e&&e.add(this.container)}updateProgress(e){if(!this.progressGraphics||(e=Math.max(0,Math.min(1,e)),this.maskGraphics&&(this.maskGraphics.destroy(),this.maskGraphics=void 0),this.progressGraphics.clearMask(),this.progressGraphics.clear(),e<=0))return;const t=f.Display.Color.Interpolate.ColorWithColor(f.Display.Color.HexStringToColor(this.config.startColor),f.Display.Color.HexStringToColor(this.config.endColor),1,e),s=f.Display.Color.GetColor(t.r,t.g,t.b);if(this.maskGraphics=this.createRadialMask(e),this.progressGraphics.lineStyle(this.config.borderWidth,s,1),this.progressGraphics.strokeRoundedRect(this.config.x-this.config.size/2,this.config.y-this.config.size/2,this.config.size,this.config.size,this.config.cornerRadius),this.maskGraphics){const i=new f.Display.Masks.GeometryMask(this.scene,this.maskGraphics);this.progressGraphics.setMask(i),this.maskGraphics.setVisible(!1)}}createBackground(){this.container&&(this.backgroundGraphics=this.scene.add.graphics(),this.container.add(this.backgroundGraphics),this.backgroundGraphics.lineStyle(this.config.borderWidth,this.config.backgroundColor,this.config.backgroundAlpha),this.backgroundGraphics.strokeRoundedRect(this.config.x-this.config.size/2,this.config.y-this.config.size/2,this.config.size,this.config.size,this.config.cornerRadius))}createProgress(){this.container&&(this.progressGraphics=this.scene.add.graphics(),this.container.add(this.progressGraphics))}createRadialMask(e){this.maskGraphics=this.scene.add.graphics();const t=-Math.PI/2,s=2*Math.PI*e,i=t+s;this.maskGraphics.fillStyle(16777215,1),this.maskGraphics.beginPath(),this.maskGraphics.moveTo(this.config.x,this.config.y);const a=this.config.size*.8;return this.maskGraphics.arc(this.config.x,this.config.y,a,t,i,!1),this.maskGraphics.closePath(),this.maskGraphics.fillPath(),this.maskGraphics}setDepth(e){this.container&&this.container.setDepth(e)}destroy(){this.maskGraphics&&(this.maskGraphics.destroy(),this.maskGraphics=void 0),this.progressGraphics&&(this.progressGraphics.destroy(),this.progressGraphics=void 0),this.backgroundGraphics&&(this.backgroundGraphics.destroy(),this.backgroundGraphics=void 0),this.container&&(this.container.destroy(),this.container=void 0)}getContainer(){return this.container}}let Vs=class extends f.Scene{animalImages=[];mainImage;optionCards=[];radialTimerUI;bonusScoreText;UIContainer;livesManager;gamePanel;isLocked=!1;isGameOver=!1;currentRoundTime=0;roundStartTime=0;roundDuration=5e3;timerUpdateEvent;socketClient=null;roomId="room-"+Date.now().toString(36);gameId="matching-mayhem";currentRoundData=null;isWaitingForServer=!1;constructor(){super("GameScene"),console.log("Matching Mayhem Game initialized")}init(){this.isGameOver=!1,this.isWaitingForServer=!1;const e=this.registry.get("gameConfig");this.socketClient=e?.socketClient||null,this.roomId=e?.roomId||"room-"+Date.now().toString(36),this.gameId=e?.gameId||"matching-mayhem",this.setupSocketEventListeners(),this.livesManager=new De(this)}setupSocketEventListeners(){this.socketClient&&(this.socketClient.addCustomEventListener("initialized",e=>{console.log("Matching Mayhem game initialized by server:",e),e.firstRound&&(console.log("First round data received from server:",e.firstRound),this.currentRoundData=e.firstRound,this.setupRoundFromServer(e.firstRound)),this.startCountdown()}),this.socketClient.addCustomEventListener("started",e=>{console.log("Matching Mayhem game started by server:",e),this.startGame()}),this.socketClient.addCustomEventListener("action_result",e=>{console.log("Action result from server:",e),e.actionType==="card_select"&&this.handleCardSelectResult(e.data)}),this.socketClient.addCustomEventListener("ended",e=>{console.log("Game ended by server:",e),this.endGame()}),this.socketClient.addCustomEventListener("game_error",e=>{console.error("Game error from server:",e)}),this.socketClient.addCustomEventListener("game_fatal_error",e=>{console.error("Fatal game error from server:",e),this.handleFatalError(e.message,e.errorType)}),console.log("Socket event listeners setup for Matching Mayhem GameScene"))}create(){this.animalImages=this.game.registry.get("animalImages")||[],this.cameras.main.setBackgroundColor(Z.BACKGROUND_COLOR);const e=this.add.container(0,0);e.setDepth(-10);const t=this.cameras.main.width,s=this.cameras.main.height,i=this.textures.createCanvas("gradientBg",t,s);if(i){const n=i.getContext(),c=n.createRadialGradient(t/2,s/2,0,t/2,s/2,s*.8);c.addColorStop(0,"#151B30"),c.addColorStop(1,"#0E0F1E"),n.fillStyle=c,n.fillRect(0,0,t,s);for(let d=0;d<5e3;d++){const u=Math.random()*t,h=Math.random()*s,g=Math.random()*2,v=Math.random()*.05;n.fillStyle=`rgba(255, 255, 255, ${v})`,n.fillRect(u,h,g,g)}i.refresh();const o=this.add.image(0,0,"gradientBg").setOrigin(0,0);e.add(o),console.log("Created gradient background as fallback")}else{const n=this.add.rectangle(0,0,this.cameras.main.width,this.cameras.main.height,921374).setOrigin(0,0);e.add(n)}const a=this.add.image(0,0,"game_background").setOrigin(0,0);a.displayWidth=this.cameras.main.width,a.displayHeight=this.cameras.main.height,e.add(a),this.gamePanel=this.add.container(0,0),this.gamePanel.setVisible(!1),this.gamePanel.setDepth(1),this.createUI(),this.initializeGame()}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("ended",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for Matching Mayhem GameScene"))}shutdown(){this.cleanupSocketEventListeners(),this.stopLocalTimer(),this.radialTimerUI&&this.radialTimerUI.destroy(),this.livesManager&&this.livesManager.destroy()}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}initializeGame(){console.log("Initializing Matching Mayhem game..."),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()&&this.socketClient.startGame()}createUI(){this.UIContainer=this.add.container(0,0),this.gamePanel.add(this.UIContainer),this.createCenter(),this.createRoundTimerUI()}createCenter(){const t=this.cameras.main.width/2,s=this.cameras.main.height*.55,i=[{x:t-110,y:s-110},{x:t+110,y:s-110},{x:t,y:s},{x:t-110,y:s+110},{x:t+110,y:s+110}];for(let d=0;d<Z.OPTION_CARDS_COUNT;d++){const u=new jt(this,i[d].x,i[d].y,Z.CARD_SIZE);this.optionCards.push(u),d!==2&&u.on("pointerdown",()=>{this.checkAnswer(u.getCardId())})}for(let d=0;d<Z.OPTION_CARDS_COUNT;d++)d!==2&&(this.gamePanel.add(this.optionCards[d]),this.optionCards[d].setDepth(1));const a=this.optionCards[2],n=this.add.graphics();n.fillStyle(1579032,1);const c=Z.CARD_SIZE*.9;n.fillRoundedRect(i[2].x-c/2,i[2].y-c/2,c,c,16),this.gamePanel.add(n),n.setDepth(8);const o=this.add.image(i[2].x,i[2].y,"card_bg").setOrigin(.5).setDisplaySize(Z.CARD_SIZE+5,Z.CARD_SIZE+5).setTint(3399167).setAlpha(.4);this.gamePanel.add(o),o.setDepth(9),this.gamePanel.add(a),a.setDepth(10),a.getCardBackground().setAlpha(1),a.disableInteractive(),console.log("Center card added to game panel"),console.log("Center card position:",a.x,a.y),console.log("Center card visible:",a.visible),console.log("Center card alpha:",a.alpha),console.log("Game panel children count:",this.gamePanel.list.length)}createRoundTimerUI(){const{width:e,height:t}=this.cameras.main;this.radialTimerUI=new qs(this,{x:e/2,y:t*.55,size:Z.CARD_SIZE*3,cornerRadius:16,borderWidth:8}),this.radialTimerUI.create(),this.radialTimerUI.setDepth(7);const s=this.radialTimerUI.getContainer();s&&this.gamePanel.add(s),this.radialTimerUI.updateProgress(1)}updateRadialTimer(e){this.radialTimerUI&&this.radialTimerUI.updateProgress(e)}startLocalTimer(){this.stopLocalTimer(),this.timerUpdateEvent=this.time.addEvent({delay:50,callback:this.updateLocalTimer,callbackScope:this,loop:!0})}stopLocalTimer(){this.timerUpdateEvent&&(this.timerUpdateEvent.destroy(),this.timerUpdateEvent=void 0)}updateLocalTimer(){if(this.isLocked||this.isGameOver){this.stopLocalTimer();return}const t=Date.now()-this.roundStartTime,s=Math.max(0,this.roundDuration-t),i=s/this.roundDuration;this.currentRoundTime=s/1e3,this.updateRadialTimer(i),s<=0&&this.stopLocalTimer()}startGame(){console.log("Starting game with server data"),w.startGame(),this.gamePanel.setVisible(!0),console.log("Game panel made visible")}setupRoundFromServer(e){console.log("Setting up round from server:",e),this.isLocked=!1,this.isWaitingForServer=!1,this.roundStartTime=e.startTime,this.roundDuration=e.timeLimit,this.currentRoundTime=e.timeLimit/1e3,this.updateRadialTimer(1),this.startLocalTimer(),this.setupCardsFromServerData(e)}setupCardsFromServerData(e){console.log("Setting up cards from server data:",e);const t=e.mainCard.imageKey;this.mainImage?.setTexture(t);for(let s=0;s<this.optionCards.length;s++){this.optionCards[s].resetSelection();const i=e.cards[s];i?(this.optionCards[s].setCardId(i.id),this.optionCards[s].setCardImage(i.imageKey),this.optionCards[s].setTint(js[i.colorIndex]),console.log(`Card ${s}: ${i.imageKey}, ID: ${i.id}, position: ${i.position}`)):console.error(`No card data provided for card ${s}`),this.optionCards[s].animateCardImage(200+s*50)}}checkAnswer(e){if(this.isLocked||this.isGameOver||this.isWaitingForServer)return;this.isLocked=!0,this.isWaitingForServer=!0;const t=this.currentRoundData?Date.now()-this.currentRoundData.startTime:0;console.log("Card selected:",e,"Reaction time:",t),this.socketClient&&this.socketClient.isConnected()?this.socketClient.sendCardSelect(e,t):(console.error("Socket client not connected - game requires server connection"),this.isLocked=!1,this.isWaitingForServer=!1)}findCardById(e){return this.optionCards.find(t=>t.getCardId()===e)||null}handleCardSelectResult(e){console.log("Handling card select result:",e),this.stopLocalTimer();const{cardId:t,isCorrect:s,points:i,newScore:a,newLives:n,gameEnded:c,nextRound:o,correctCardId:d}=e,u=this.findCardById(t),h=this.findCardById(d);h&&d!==t&&h.markSelected(!0),s?(this.sound.play("correct"),u&&u.markSelected(!0),w.updateScore(a),this.showBonusText(`Good Choice +${i}`,!0)):(this.sound.play("wrong"),u&&u.markSelected(!1),w.updateScore(a),w.updateLives(n),this.livesManager.deductHeart(this.cameras.main.width/2,this.cameras.main.height/2,this.gamePanel),this.showBonusText(`Bad Choice -${i}`,!1)),c?this.time.delayedCall(1e3,()=>{this.endGame()}):o?this.time.delayedCall(780,()=>{this.setupRoundFromServer(o)}):this.time.delayedCall(500,()=>{this.isLocked=!1,this.isWaitingForServer=!1})}showBonusText(e,t){!this.bonusScoreText||!(this.bonusScoreText instanceof f.GameObjects.Text)?(this.bonusScoreText=this.add.text(this.cameras.main.width/2,this.cameras.main.height/2.5,e,{fontFamily:"Arial",fontSize:"32px",fontStyle:"italic",color:t?"#4FFFAA":"#FF4F59",stroke:"#000000",strokeThickness:3,shadow:{offsetX:1,offsetY:1,color:"#000000",blur:2,stroke:!0,fill:!0}}).setOrigin(.5).setDepth(100).setAlpha(0),this.gamePanel.add(this.bonusScoreText)):(this.bonusScoreText.setText(e),this.bonusScoreText.setColor(t?"#4FFFAA":"#FF4F59"),this.bonusScoreText.setPosition(this.cameras.main.width/2,this.cameras.main.height/2.5));const s=this.cameras.main.height/2.5-50;this.tweens.killTweensOf(this.bonusScoreText),this.bonusScoreText.setAlpha(0),this.bonusScoreText.setPosition(this.cameras.main.width/2,this.cameras.main.height/2.5),this.tweens.add({targets:this.bonusScoreText,alpha:1,duration:200,ease:"Linear"}),this.tweens.add({targets:this.bonusScoreText,y:s,duration:700,ease:"Cubic.easeOut"}),this.tweens.add({targets:this.bonusScoreText,alpha:0,delay:600,duration:300,ease:"Linear"})}endGame(){this.isGameOver||(this.stopLocalTimer(),this.isGameOver=!0,w.endGame(),this.sound.play("end"))}},Ks=class extends f.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene")}init(e){this.score=e.score||0}create(){this.ticTaps=new ht,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===f.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===f.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,Zs(o,2,2,e,t,18),a.refresh();const u=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(u)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const c=new f.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(c,f.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const c="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(c)&&this.textures.remove(c);const o=Math.max(400,s.length*i*.7),d=i*1.5,u=this.textures.createCanvas(c,o,d);if(!u)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=u.getContext(),g=h.createLinearGradient(0,0,o,d*.5);return a?(g.addColorStop(0,"#4cffae"),g.addColorStop(.4,"#32c4ff"),g.addColorStop(1,"#5c67ff")):n?(g.addColorStop(0,"#32c4ff"),g.addColorStop(.5,"#7f54ff"),g.addColorStop(1,"#b63efc")):(g.addColorStop(0,"#33DDFF"),g.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=g,h.fillText(s,o/2,d/2),u.refresh(),this.add.image(e,t,c).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}};function Zs(l,e,t,s,i,a,n,c){l.beginPath(),l.moveTo(e+a,t),l.lineTo(e+s-a,t),l.quadraticCurveTo(e+s,t,e+s,t+a),l.lineTo(e+s,t+i-a),l.quadraticCurveTo(e+s,t+i,e+s-a,t+i),l.lineTo(e+a,t+i),l.quadraticCurveTo(e,t+i,e,t+i-a),l.lineTo(e,t+a),l.quadraticCurveTo(e,t,e+a,t),l.closePath(),l.stroke()}class Qs{config;gameInstance=null;constructor(e){this.config=e}async init(){console.log("Initializing MatchingMayhemGame game...");const e={type:f.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:this.config.containerId,scene:[Ys,Xs,Vs,Ks],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:f.Scale.EXPAND,autoCenter:f.Scale.CENTER_BOTH},render:{antialias:!0,pixelArt:!1,roundPixels:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new f.Game(e),this.gameInstance&&this.gameInstance.registry.set("gameConfig",this.config)}start(){console.log("Starting MatchingMayhemGame game..."),this.gameInstance?.scene.start("GameScene")}pause(){console.log("Pausing MatchingMayhemGame game...")}resume(){console.log("Resuming MatchingMayhemGame game...")}destroy(){console.log("Destroying MatchingMayhemGame game..."),this.gameInstance&&(this.gameInstance=null)}getCurrentScore(){return 0}}let Js=class extends f.Scene{constructor(){super("PreloadScene")}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),this.load.image("countdown-1","/assets/images/countdown-1.png"),this.load.image("countdown-2","/assets/images/countdown-2.png"),this.load.image("countdown-3","/assets/images/countdown-3.png"),this.load.image("countdown-go","/assets/images/countdown-go.png"),this.load.svg("heart","/assets/images/mdi--heart.svg"),this.load.svg("heart_outline","/assets/images/mdi-light--heart.svg"),this.load.svg("heart_broken","/assets/images/mdi--heart-broken.svg"),this.load.image("game_name","/assets-numbers/images/game_name.svg"),this.load.image("button_bg","/assets/images/button_bg.svg"),this.load.image("game_start","/assets/images/game_start.png"),this.load.image("game_background","/assets/images/game_bg.png"),this.load.image("game_over","/assets/images/game_over.svg"),this.load.image("back_to_lobby","/assets/images/back_to_lobby.png"),this.load.image("timer_bg","/assets/images/timer_bg.svg"),this.load.image("timer_icon","/assets/images/timer_icon.png"),this.load.image("timer_countdown_bg","/assets/images/timer_countdown_bg.png"),this.load.image("circle","/assets-numbers/images/circle.png"),this.load.audio("countdown",["/assets/audio/countdown.ogg","/assets/audio/countdown.mp3"]),this.load.audio("click",["/assets/audio/click.ogg","/assets/audio/click.mp3"]),this.load.audio("go",["/assets/sounds/go.ogg","/assets/sounds/go.mp3"]),this.load.audio("collect",["/assets-numbers/sounds/collect.ogg","/assets-numbers/sounds/collect.mp3"]),this.load.audio("complete",["/assets-numbers/sounds/complete.ogg","/assets-numbers/sounds/complete.mp3"]),this.load.audio("error",["/assets-numbers/sounds/error.ogg","/assets-numbers/sounds/error.mp3"]),this.load.audio("timeout",["/assets-numbers/sounds/timeout.ogg","/assets-numbers/sounds/timeout.mp3"])}create(){}};class Ne{isWebGL;constructor(){this.isWebGL=this.checkIfWebGL()}checkIfWebGL(){return typeof window<"u"&&window.parent&&window.parent!==window}notifyGameReady(){this.sendMessage({type:"gameReady"})}sendScore(e){this.sendMessage({type:"gameScore",score:e})}notifyGameQuit(){this.sendMessage({type:"gameQuit"})}sendMessage(e){this.isWebGL&&window.parent&&typeof window.parent.postMessage=="function"&&(window.parent.postMessage(e,"*"),console.log("Message sent to parent:",e))}}let $s=class extends f.Scene{ticTaps;startButton;isStarting=!1;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.ticTaps=new Ne,this.textures.exists("game_background")?this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(e,t):(this.cameras.main.setBackgroundColor("#000000"),console.warn("game_background asset missing, using fallback"));let s,i;this.textures.exists("game_name")?(s=this.add.image(e/2,t*.25,"game_name").setOrigin(.5),i=Math.min(e*.7/s.width,.8),s.setScale(i)):(s=this.add.text(e/2,t*.25,"NUMBERS GAME",{fontFamily:"NeorisTrialBold, Arial",fontSize:"48px",fontStyle:"bold",color:"#ffffff"}).setOrigin(.5),i=1,console.warn("game_name asset missing, using fallback text")),this.tweens.add({targets:s,scaleX:i*1.02,scaleY:i*1.02,duration:1500,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"});let a,n;if(this.textures.exists("button_bg"))if(this.startButton=this.add.image(e/2,t*.6,"button_bg").setOrigin(.5),a=Math.min(e*.6/this.startButton.width,.4),this.startButton.setScale(a),this.textures.exists("game_start")){n=this.add.image(this.startButton.x,this.startButton.y-5,"game_start").setOrigin(.5);const c=this.startButton.displayWidth*.6/n.width;n.setScale(c)}else n=this.add.text(this.startButton.x,this.startButton.y,"START",{fontFamily:"NeorisTrialBold, Arial",fontSize:"32px",fontStyle:"bold",color:"#ffffff"}).setOrigin(.5),console.warn("game_start asset missing, using fallback text");else{const c=this.add.graphics();c.fillStyle(31436,1),c.lineStyle(2,52479,1),c.fillRoundedRect(-120,-40,240,80,20),c.strokeRoundedRect(-120,-40,240,80,20),this.startButton=this.add.container(e/2,t*.6,[c]),a=1,n=this.add.text(this.startButton.x,this.startButton.y,"START GAME",{fontFamily:"NeorisTrialBold, Arial",fontSize:"32px",fontStyle:"bold",color:"#ffffff"}).setOrigin(.5),console.warn("button_bg asset missing, using fallback button")}this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerover",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a*1.05,scaleY:a*1.05,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerout",()=>{this.tweens.add({targets:[this.startButton,n],scaleX:a,scaleY:a,duration:150,ease:"Sine.easeOut"})}),this.startButton.on("pointerdown",()=>{try{this.sound.get("countdown")&&this.sound.play("countdown",{volume:te.SOUND_VOLUME})}catch(c){console.warn("Sound playback failed:",c)}this.tweens.add({targets:[this.startButton,n],scaleX:a*.95,scaleY:a*.95,duration:te.FLASH_DURATION,yoyo:!0,onComplete:()=>this.startGameCountdown(e,t)})})}startGameCountdown(e,t){if(this.isStarting)return;this.isStarting=!0;const s=this.add.rectangle(e/2,t/2,e,t,16777215).setAlpha(0).setOrigin(.5);s.setDepth(1e3),this.tweens.add({targets:s,alpha:.8,duration:te.FLASH_DURATION,ease:"Sine.easeOut",onComplete:()=>{this.sound.get("go")&&this.sound.play("go",{volume:te.SOUND_VOLUME}),this.tweens.add({targets:s,alpha:0,delay:te.TRANSITION_DELAY,duration:te.TRANSITION_FADE_DURATION,ease:"Sine.easeIn",onComplete:()=>{this.ticTaps.notifyGameReady(),this.scene.start("GameScene")}})}})}};class ei{scene;config;score;scoreText;scoreLabel;container;events;constructor(e,t={}){this.scene=e,this.events=new f.Events.EventEmitter,this.config={initialScore:t.initialScore??0,fontFamily:t.fontFamily??"Arial",fontSize:t.fontSize??"80px",labelFontSize:t.labelFontSize??"28px",scoreColor:t.scoreColor??"#33DDFF",labelColor:t.labelColor??"#FFFFFF",animationColor:t.animationColor??"#ffff00",animationDuration:t.animationDuration??800},this.score=this.config.initialScore}createUI(e,t,s){this.container=this.scene.add.container(0,0),this.scoreLabel=this.scene.add.text(e,t-30,"Total Point",{fontFamily:this.config.fontFamily,fontSize:this.config.labelFontSize,fontStyle:"bold",color:this.config.labelColor}).setOrigin(.5),this.scoreText=this.scene.add.text(e,t+30,this.score.toString(),{fontFamily:this.config.fontFamily,fontSize:this.config.fontSize,fontStyle:"bold",color:this.config.scoreColor}).setOrigin(.5),this.container.add([this.scoreLabel,this.scoreText]),s&&s.add(this.container)}addPoints(e,t){this.score+=e,this.updateScoreDisplay(),t&&this.createScoreAnimation(t),this.events.emit("scoreChanged",this.score,e)}setScore(e){const t=this.score;this.score=e,this.updateScoreDisplay(),this.events.emit("scoreChanged",this.score,this.score-t)}getScore(){return this.score}reset(){this.score=this.config.initialScore,this.updateScoreDisplay(),this.events.emit("scoreReset",this.score)}updateScoreDisplay(){this.scoreText&&this.scoreText.setText(this.score.toString())}createScoreAnimation(e){const t=this.scene.add.text(e.startX,e.startY,`+${e.points}`,{fontFamily:this.config.fontFamily,fontSize:"24px",color:e.color??this.config.animationColor,stroke:"#000000",strokeThickness:3});t.setOrigin(.5),this.scene.tweens.add({targets:t,y:e.startY-50,alpha:0,scale:1.2,duration:e.duration??this.config.animationDuration,ease:"Power2",onComplete:()=>{t.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.scoreText=void 0,this.scoreLabel=void 0,this.container=void 0}}class ti{scene;config;lives;hearts=[];container;events;constructor(e,t={}){this.scene=e,this.events=new f.Events.EventEmitter,this.config={initialLives:t.initialLives??3},this.lives=this.config.initialLives}createUI(e,t,s){this.container=this.scene.add.container(e,t);const a=-((this.lives-1)*40)/2;for(let n=0;n<this.lives;n++){let c=this.scene.add.image(a+n*40,0,"heart").setOrigin(.5).setScale(1.5);this.hearts.push(c)}this.container.add(this.hearts),s&&s.add(this.container)}deductHeart(e,t){this.lives--,this.hearts[this.lives]&&this.hearts[this.lives].setTexture("heart_outline"),e!==void 0&&t!==void 0&&this.container&&this.createFlyingHeartAnimation(e,t),this.events.emit("heartDeducted",this.lives)}createFlyingHeartAnimation(e,t){if(!this.container)return;const s=this.scene.add.image(e,t,"heart_broken").setOrigin(.5).setScale(1.5).setAlpha(.4);this.scene.tweens.add({targets:s,y:t-200,scale:3,alpha:.8,duration:600,ease:"Power2",onComplete:()=>{s.destroy()}})}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}destroy(){this.events.removeAllListeners(),this.container&&this.container.destroy(),this.container=void 0}}let si=class extends f.Scene{ticTaps;gamePanel;countdownPanel;socketClient;numberObjects=[];currentIndex=0;isLocked=!1;roundText;currentSequence=[];scoreManager;livesManager;line;confettiEmitter;lastTappedValue=-1;constructor(){super("GameScene")}playSound(e){try{this.sound.play(e)}catch(t){console.warn("Sound playback failed:",t)}}init(e){this.socketClient=e.socketClient,this.currentIndex=0,this.isLocked=!1,this.scoreManager=new ei(this,{initialScore:0,fontSize:"80px",scoreColor:"#33DDFF"}),this.livesManager=new ti(this),this.socketClient.addCustomEventListener("initialized",t=>{console.log("Number Sequence game initialized by server:",t),t.currentSequence&&(this.currentSequence=t.currentSequence,this.generateNumbers(t.currentSequence)),this.startCountdown()}),this.socketClient.addCustomEventListener("started",t=>{console.log("Number Sequence game started by server:",t),this.countdownPanel.visible=!1,this.gamePanel.visible=!0}),this.socketClient.addCustomEventListener("action_result",t=>{if(!t||t.actionType!=="number_select")return;const s=t.data;if(s.isCorrect){const i=this.lastTappedValue,a=this.numberObjects.find(n=>n.numberValue===i);a&&(a.disableInteractive(),a.setActive(!0),this.updateLine(a.x,a.y),this.playSound("collect"),a.playDisappearAnimation(()=>{})),this.currentIndex=s.currentIndex,w.updateScore(s.newScore),this.updateGradientRoundText(`${this.currentIndex} to ${this.currentSequence.length}`),s.nextSequence&&(this.currentSequence=s.nextSequence,this.generateNumbers(s.nextSequence))}else{const i=this.lastTappedValue,a=this.numberObjects.find(n=>n.numberValue===i)||this.numberObjects.find(n=>n.numberValue===this.currentIndex);if(a?(a.setError(),this.playSound("error"),w.updateLives(s.newLives),this.livesManager.deductHeart(a.x,a.y)):this.livesManager.deductHeart(),this.currentIndex=s.currentIndex,s.gameEnded){this.isLocked=!0;return}}}),this.socketClient.addCustomEventListener("game_error",t=>{console.error("Game error from server:",t)}),this.socketClient.addCustomEventListener("game_fatal_error",t=>{console.error("Fatal game error from server:",t),this.handleFatalError(t.message,t.errorType)}),this.socketClient.addCustomEventListener("ended",t=>{this.handleServerEnded(t)}),this.livesManager.on("heartDeducted",t=>{t===0&&this.timeOut()})}create(){this.ticTaps=new Ne,this.add.image(this.cameras.main.width/2,this.cameras.main.height/2,"game_background").setOrigin(.5).setDisplaySize(this.cameras.main.width,this.cameras.main.height),this.createGamePanel(),this.line=this.add.graphics(),this.createConfettiSystem(),this.time.delayedCall(100,()=>{this.initializeGame()})}cleanupSocketEventListeners(){this.socketClient&&(this.socketClient.removeCustomEventListener("initialized",()=>{}),this.socketClient.removeCustomEventListener("started",()=>{}),this.socketClient.removeCustomEventListener("action_result",()=>{}),this.socketClient.removeCustomEventListener("game_error",()=>{}),this.socketClient.removeCustomEventListener("game_fatal_error",()=>{}),console.log("Socket event listeners cleaned up for Number Sequence GameScene"))}shutdown(){this.cleanupSocketEventListeners(),this.scoreManager&&this.scoreManager.destroy(),this.livesManager&&this.livesManager.destroy(),this.confettiEmitter&&this.confettiEmitter.stop(),this.numberObjects.forEach(e=>e.destroy()),this.numberObjects=[]}createGamePanel(){this.gamePanel=this.add.container(0,0),this.gamePanel.visible=!1;const{width:e,height:t}=this.cameras.main;this.scoreManager.createUI(e/2,t*.09,this.gamePanel),this.createGradientRoundText(),this.gamePanel.add([])}createGradientRoundText(){this.updateGradientRoundText("0 to 6")}updateGradientRoundText(e){const{height:t}=this.cameras.main,s="roundText-"+e.replace(/\s+/g,"-");this.textures.exists(s)&&this.textures.remove(s);const i=24,a=Math.max(120,e.length*i*.6),n=i*1.5,c=this.textures.createCanvas(s,a,n);if(!c){console.error("Failed to create gradient round text canvas"),this.roundText&&this.roundText.destroy(),this.roundText=this.add.text(50,t-20,e,{fontSize:"24px",fontFamily:"Arial",color:"#999999"}).setOrigin(0,1),this.gamePanel.add(this.roundText);return}const o=c.getContext(),d=o.createLinearGradient(0,0,a,n*.5);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.font=`bold ${i}px Arial`,o.textAlign="left",o.textBaseline="middle",o.fillStyle=d,o.fillText(e,0,n/2),c.refresh(),this.roundText&&this.roundText.destroy(),this.roundText=this.add.image(50,t-20,s).setOrigin(0,1),this.gamePanel.add(this.roundText)}initializeGame(){console.log("Initializing Number Sequence game..."),this.socketClient&&this.socketClient.isConnected()?this.socketClient.initGame():console.error("No server connection available - cannot initialize game")}async startCountdown(){for(let e=0;e<4;e++){try{this.sound.play(e===3?"go":"countdown")}catch(t){console.warn("Sound playback failed:",t)}await new Promise(t=>{this.time.delayedCall(1300,()=>t())})}this.socketClient&&this.socketClient.isConnected()?this.socketClient.startGame():console.error("No server connection available - cannot start game")}generateNumbers(e){this.numberObjects.forEach(s=>s.destroy()),this.numberObjects=[],this.currentIndex=0,this.line.clear(),this.updateGradientRoundText(`0 to ${e.length}`);const t=this.generatePositions(e.length);e.forEach((s,i)=>{this.time.delayedCall(i*100,()=>{const a=new Yt(this,t[i].x,t[i].y,s);this.numberObjects.push(a)})})}generatePositions(e){const{width:t,height:s}=this.cameras.main,i=te.CIRCLE_RADIUS,a=i*2+20,n=s*.3,c=s*.95,o=t*.06,d=t*.94,u=[],h=(k,_)=>{if(k-i<o||k+i>d||_-i<n||_+i>c)return!1;for(const T of u)if(Math.sqrt(Math.pow(k-T.x,2)+Math.pow(_-T.y,2))<a)return!1;return!0};let g=0;const v=1e3;for(;u.length<e&&g<v;){const k=o+i+Math.random()*(d-o-i*2),_=n+i+Math.random()*(c-n-i*2);h(k,_)&&u.push({x:k,y:_}),g++}return u.length<e?(console.warn("Using grid fallback for remaining positions"),this.generateGridBasedPositions(e)):u}generateGridBasedPositions(e){const{width:t,height:s}=this.cameras.main,i=te.CIRCLE_RADIUS*2,a=Math.min(5,Math.floor(t/i)),n=Math.ceil(e/a),c=a*i,o=n*i,d=(t-c)/2+i/2,u=s*.75,v=s*.2+(u-o)/2,k=[];for(let T=0;T<e;T++){const C=Math.floor(T/a),E=T%a;k.push({x:d+E*i,y:v+C*i})}return[...k].sort(()=>Math.random()-.5)}timeOut(){this.isLocked=!0,this.playSound("timeout"),this.confettiEmitter.start(),w.endGame()}handleServerEnded(e){this.isLocked=!0;try{this.playSound("complete")}catch{}w.endGame()}handleNumberClick(e){this.isLocked||(this.lastTappedValue=e.numberValue,this.socketClient&&this.socketClient.isConnected()?this.socketClient.sendNumberSelect(e.numberValue):console.error("Socket client not connected - game requires server connection"))}handleFatalError(e,t){console.error(`Fatal error (${t}): ${e}`),this.isLocked=!0,this.scene.pause(),typeof window<"u"&&window.showGameError?window.showGameError(e,t):console.error("Error modal not available, error:",e)}updateLine(e,t){this.currentIndex===1&&(this.line.lineStyle(4,52479),this.line.moveTo(this.numberObjects[0].x,this.numberObjects[0].y)),this.line.lineTo(e,t)}createConfettiSystem(){const{width:e}=this.cameras.main,t=this.textures.createCanvas("particleTexture",10,10);if(!t){console.warn("Failed to create particle texture, using fallback"),this.confettiEmitter=this.add.particles(e/2,-50,"",{speed:{min:150,max:350},scale:{start:.8,end:0},lifespan:{min:1500,max:2500},gravityY:400,rotate:{min:-180,max:180},frequency:50,quantity:3,tint:[16711680,65280,255,16776960,16711935,65535],blendMode:"ADD",emitting:!1});return}const s=t.getContext();s.fillStyle="#ffffff",s.fillRect(0,0,10,10),t.refresh(),this.confettiEmitter=this.add.particles(e/2,-50,"particleTexture",{speed:{min:150,max:350},scale:{start:.8,end:0},lifespan:{min:1500,max:2500},gravityY:400,scaleX:{onUpdate:(i,a,n)=>Math.sin(n*Math.PI*8)},rotate:{min:-180,max:180},frequency:50,quantity:3,tint:[16711680,65280,255,16776960,16711935,65535],blendMode:"ADD",emitting:!1})}},ii=class extends f.Scene{ticTaps;score=0;backToLobbyButton;constructor(){super("GameEndScene")}init(e){this.score=e.score||0}create(){this.ticTaps=new Ne,this.add.image(0,0,"game_background").setOrigin(0,0).setDisplaySize(this.cameras.main.width,this.cameras.main.height);const e=this.cameras.main.width*.8,t=this.cameras.main.height*.6;if(this.sys.game.renderer.type===f.WEBGL){const n=this.add.graphics();n.fillStyle(0,.3),n.fillRoundedRect(this.cameras.main.width/2-e/2-2,this.cameras.main.height/2-t/2-2,e+4,t+4,20),n.postFX.addBlur(0,0,1,2,1,1)}const s=this.add.graphics();s.fillStyle(1712945,.4),s.fillRoundedRect(this.cameras.main.width/2-e/2,this.cameras.main.height/2-t/2,e,t,20);const i=this.add.image(this.cameras.main.width/2,this.cameras.main.height/2-t*.5,"game_over").setOrigin(.5),a=e*.8/i.width;i.setScale(a),this.sys.game.renderer.type===f.WEBGL&&i.postFX.addGlow(4980654,1,0,!1,.1,15),this.add.text(this.cameras.main.width/2,this.cameras.main.height/2-100,"SCORE",{fontFamily:"Arial",fontSize:"30px",fontStyle:"bold",color:"#FFFFFF"}).setOrigin(.5),this.createGradientText(this.cameras.main.width/2,this.cameras.main.height/2,this.score.toString(),90,!0),this.createBackToLobbyButton()}createBackToLobbyButton(){const e=this.cameras.main.width*.7,t=80,s=this.cameras.main.width/2,i=this.cameras.main.height/2+this.cameras.main.height*.2;this.backToLobbyButton=this.add.container(s,i);const a=this.textures.createCanvas("buttonBorder",e+4,t+4);if(a){const o=a.getContext(),d=o.createLinearGradient(0,0,e+4,0);d.addColorStop(0,"#32c4ff"),d.addColorStop(.5,"#7f54ff"),d.addColorStop(1,"#b63efc"),o.strokeStyle=d,o.lineWidth=2.5,ai(o,2,2,e,t,18),a.refresh();const u=this.add.image(0,0,"buttonBorder").setOrigin(.5);this.backToLobbyButton.add(u)}const n=this.add.graphics();if(n.fillStyle(1185311,1),n.fillRoundedRect(-e/2+2,-t/2+2,e-4,t-4,16),this.backToLobbyButton.add(n),this.textures.exists("back_to_lobby")){const o=this.add.image(0,0,"back_to_lobby").setOrigin(.5),d=Math.min(e*.7/o.width,t*.6/o.height);o.setScale(d),this.backToLobbyButton.add(o)}else{const o=this.createGradientText(0,0,"BACK TO LOBBY",28,!1,!0);this.backToLobbyButton.add(o)}const c=new f.Geom.Rectangle(-e/2,-t/2,e,t);this.backToLobbyButton.setInteractive(c,f.Geom.Rectangle.Contains),this.backToLobbyButton.on("pointerover",()=>{this.backToLobbyButton.setScale(1.05)}),this.backToLobbyButton.on("pointerout",()=>{this.backToLobbyButton.setScale(1)}),this.backToLobbyButton.on("pointerdown",()=>{this.sound.get("laser")?this.sound.play("laser",{volume:.7}):this.sound.get("countdown")&&this.sound.play("countdown",{volume:.7}),this.backToLobbyButton.setScale(.95),this.time.delayedCall(100,()=>{this.backToLobbyButton.setScale(1),this.endGame()})})}createGradientText(e,t,s,i=32,a=!1,n=!1){const c="gradientText-"+s.replace(/\s+/g,"-")+"-"+i+(a?"-score":"")+(n?"-button":"");this.textures.exists(c)&&this.textures.remove(c);const o=Math.max(400,s.length*i*.7),d=i*1.5,u=this.textures.createCanvas(c,o,d);if(!u)return console.error("Failed to create gradient text canvas"),this.add.image(e,t,"").setOrigin(.5);const h=u.getContext(),g=h.createLinearGradient(0,0,o,d*.5);return a?(g.addColorStop(0,"#4cffae"),g.addColorStop(.4,"#32c4ff"),g.addColorStop(1,"#5c67ff")):n?(g.addColorStop(0,"#32c4ff"),g.addColorStop(.5,"#7f54ff"),g.addColorStop(1,"#b63efc")):(g.addColorStop(0,"#33DDFF"),g.addColorStop(1,"#664DFF")),h.font=`bold ${i}px Arial`,h.textAlign="center",h.textBaseline="middle",a&&(h.strokeStyle="rgba(255, 255, 255, 0.9)",h.lineWidth=5,h.strokeText(s,o/2,d/2)),h.fillStyle=g,h.fillText(s,o/2,d/2),u.refresh(),this.add.image(e,t,c).setOrigin(.5)}endGame(){const e=this.add.rectangle(this.cameras.main.width/2,this.cameras.main.height/2,this.cameras.main.width,this.cameras.main.height,16777215).setAlpha(0).setOrigin(.5);e.setDepth(1e3),this.ticTaps.sendScore(this.score),this.ticTaps.notifyGameQuit(),this.tweens.add({targets:e,alpha:.8,duration:100,ease:"Sine.easeOut",onComplete:()=>{this.tweens.add({targets:e,alpha:0,delay:50,duration:250,ease:"Sine.easeIn",onComplete:()=>{this.scene.start("GameStartScene")}})}})}};function ai(l,e,t,s,i,a,n,c){l.beginPath(),l.moveTo(e+a,t),l.lineTo(e+s-a,t),l.quadraticCurveTo(e+s,t,e+s,t+a),l.lineTo(e+s,t+i-a),l.quadraticCurveTo(e+s,t+i,e+s-a,t+i),l.lineTo(e+a,t+i),l.quadraticCurveTo(e,t+i,e,t+i-a),l.lineTo(e,t+a),l.quadraticCurveTo(e,t,e+a,t),l.closePath(),l.stroke()}class ni{config;gameInstance=null;constructor(e){this.config=e}async init(){console.log("Initializing NumberSequence game...");const e={type:f.AUTO,width:540,height:960,backgroundColor:"#0E0F1E",parent:this.config.containerId,scene:[Js,$s,si,ii],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:f.Scale.EXPAND,autoCenter:f.Scale.CENTER_BOTH},render:{antialias:!0,pixelArt:!1,roundPixels:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new f.Game(e),this.gameInstance&&this.gameInstance.registry.set("gameConfig",this.config)}start(){console.log("Starting NumberSequence game..."),this.gameInstance?.scene.start("GameScene",{socketClient:this.config.socketClient})}pause(){console.log("Pausing NumberSequence game...")}resume(){console.log("Resuming NumberSequence game...")}destroy(){console.log("Destroying NumberSequence game..."),this.gameInstance&&(this.gameInstance=null)}getCurrentScore(){return 0}}const Ie=[];Me.handleByNamedList(he.Environment,Ie);async function oi(l){if(!l)for(let e=0;e<Ie.length;e++){const t=Ie[e];if(t.value.test()){await t.value.load();return}}}let de;function ri(){if(typeof de=="boolean")return de;try{de=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch{de=!1}return de}var mt=(l=>(l[l.NONE=0]="NONE",l[l.COLOR=16384]="COLOR",l[l.STENCIL=1024]="STENCIL",l[l.DEPTH=256]="DEPTH",l[l.COLOR_DEPTH=16640]="COLOR_DEPTH",l[l.COLOR_STENCIL=17408]="COLOR_STENCIL",l[l.DEPTH_STENCIL=1280]="DEPTH_STENCIL",l[l.ALL=17664]="ALL",l))(mt||{});class li{constructor(e){this.items=[],this._name=e}emit(e,t,s,i,a,n,c,o){const{name:d,items:u}=this;for(let h=0,g=u.length;h<g;h++)u[h][d](e,t,s,i,a,n,c,o);return this}add(e){return e[this._name]&&(this.remove(e),this.items.push(e)),this}remove(e){const t=this.items.indexOf(e);return t!==-1&&this.items.splice(t,1),this}contains(e){return this.items.indexOf(e)!==-1}removeAll(){return this.items.length=0,this}destroy(){this.removeAll(),this.items=null,this._name=null}get empty(){return this.items.length===0}get name(){return this._name}}const ci=["init","destroy","contextChange","resolutionChange","resetState","renderEnd","renderStart","render","update","postrender","prerender"],gt=class ut extends Xt{constructor(e){super(),this.uid=qt("renderer"),this.runners=Object.create(null),this.renderPipes=Object.create(null),this._initOptions={},this._systemsHash=Object.create(null),this.type=e.type,this.name=e.name,this.config=e;const t=[...ci,...this.config.runners??[]];this._addRunners(...t),this._unsafeEvalCheck()}async init(e={}){const t=e.skipExtensionImports===!0?!0:e.manageImports===!1;await oi(t),this._addSystems(this.config.systems),this._addPipes(this.config.renderPipes,this.config.renderPipeAdaptors);for(const s in this._systemsHash)e={...this._systemsHash[s].constructor.defaultOptions,...e};e={...ut.defaultOptions,...e},this._roundPixels=e.roundPixels?1:0;for(let s=0;s<this.runners.init.items.length;s++)await this.runners.init.items[s].init(e);this._initOptions=e}render(e,t){let s=e;if(s instanceof le&&(s={container:s},t&&(Ce(we,"passing a second argument is deprecated, please use render options instead"),s.target=t.renderTexture)),s.target||(s.target=this.view.renderTarget),s.target===this.view.renderTarget&&(this._lastObjectRendered=s.container,s.clearColor??(s.clearColor=this.background.colorRgba),s.clear??(s.clear=this.background.clearBeforeRender)),s.clearColor){const i=Array.isArray(s.clearColor)&&s.clearColor.length===4;s.clearColor=i?s.clearColor:_e.shared.setValue(s.clearColor).toArray()}s.transform||(s.container.updateLocalTransform(),s.transform=s.container.localTransform),s.container.visible&&(s.container.enableRenderGroup(),this.runners.prerender.emit(s),this.runners.renderStart.emit(s),this.runners.render.emit(s),this.runners.renderEnd.emit(s),this.runners.postrender.emit(s))}resize(e,t,s){const i=this.view.resolution;this.view.resize(e,t,s),this.emit("resize",this.view.screen.width,this.view.screen.height,this.view.resolution),s!==void 0&&s!==i&&this.runners.resolutionChange.emit(s)}clear(e={}){const t=this;e.target||(e.target=t.renderTarget.renderTarget),e.clearColor||(e.clearColor=this.background.colorRgba),e.clear??(e.clear=mt.ALL);const{clear:s,clearColor:i,target:a}=e;_e.shared.setValue(i??this.background.colorRgba),t.renderTarget.clear(a,s,_e.shared.toArray())}get resolution(){return this.view.resolution}set resolution(e){this.view.resolution=e,this.runners.resolutionChange.emit(e)}get width(){return this.view.texture.frame.width}get height(){return this.view.texture.frame.height}get canvas(){return this.view.canvas}get lastObjectRendered(){return this._lastObjectRendered}get renderingToScreen(){return this.renderTarget.renderingToScreen}get screen(){return this.view.screen}_addRunners(...e){e.forEach(t=>{this.runners[t]=new li(t)})}_addSystems(e){let t;for(t in e){const s=e[t];this._addSystem(s.value,s.name)}}_addSystem(e,t){const s=new e(this);if(this[t])throw new Error(`Whoops! The name "${t}" is already in use`);this[t]=s,this._systemsHash[t]=s;for(const i in this.runners)this.runners[i].add(s);return this}_addPipes(e,t){const s=t.reduce((i,a)=>(i[a.name]=a.value,i),{});e.forEach(i=>{const a=i.value,n=i.name,c=s[n];this.renderPipes[n]=new a(this,c?new c:null),this.runners.destroy.add(this.renderPipes[n])})}destroy(e=!1){this.runners.destroy.items.reverse(),this.runners.destroy.emit(e),Object.values(this.runners).forEach(t=>{t.destroy()}),(e===!0||typeof e=="object"&&e.releaseGlobalResources)&&Vt.release(),this._systemsHash=null,this.renderPipes=null}generateTexture(e){return this.textureGenerator.generateTexture(e)}get roundPixels(){return!!this._roundPixels}_unsafeEvalCheck(){if(!ri())throw new Error("Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.")}resetState(){this.runners.resetState.emit()}};gt.defaultOptions={resolution:1,failIfMajorPerformanceCaveat:!1,roundPixels:!1};let ft=gt,fe;function di(l){return fe!==void 0||(fe=(()=>{const e={stencil:!0,failIfMajorPerformanceCaveat:l??ft.defaultOptions.failIfMajorPerformanceCaveat};try{if(!Fe.get().getWebGLRenderingContext())return!1;let s=Fe.get().createCanvas().getContext("webgl",e);const i=!!s?.getContextAttributes()?.stencil;if(s){const a=s.getExtension("WEBGL_lose_context");a&&a.loseContext()}return s=null,i}catch{return!1}})()),fe}let pe;async function hi(l={}){return pe!==void 0||(pe=await(async()=>{const e=Fe.get().getNavigator().gpu;if(!e)return!1;try{return await(await e.requestAdapter(l)).requestDevice(),!0}catch{return!1}})()),pe}const Je=["webgl","webgpu","canvas"];async function mi(l){let e=[];l.preference?(e.push(l.preference),Je.forEach(a=>{a!==l.preference&&e.push(a)})):e=Je.slice();let t,s={};for(let a=0;a<e.length;a++){const n=e[a];if(n==="webgpu"&&await hi()){const{WebGPURenderer:c}=await Ye(async()=>{const{WebGPURenderer:o}=await import("./Dq8x2eD_.js");return{WebGPURenderer:o}},__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10]),import.meta.url);t=c,s={...l,...l.webgpu};break}else if(n==="webgl"&&di(l.failIfMajorPerformanceCaveat??ft.defaultOptions.failIfMajorPerformanceCaveat)){const{WebGLRenderer:c}=await Ye(async()=>{const{WebGLRenderer:o}=await import("./KN2rleQP.js");return{WebGLRenderer:o}},__vite__mapDeps([11,1,2,3,4,5,6,7,8,9,10]),import.meta.url);t=c,s={...l,...l.webgl};break}else if(n==="canvas")throw s={...l},new Error("CanvasRenderer is not yet implemented")}if(delete s.webgpu,delete s.webgl,!t)throw new Error("No available renderer for the current environment");const i=new t;return await i.init(s),i}const pt="8.13.2";class vt{static init(){globalThis.__PIXI_APP_INIT__?.(this,pt)}static destroy(){}}vt.extension=he.Application;class gi{constructor(e){this._renderer=e}init(){globalThis.__PIXI_RENDERER_INIT__?.(this._renderer,pt)}destroy(){this._renderer=null}}gi.extension={type:[he.WebGLSystem,he.WebGPUSystem],name:"initHook",priority:-10};const bt=class Ae{constructor(...e){this.stage=new le,e[0]!==void 0&&Ce(we,"Application constructor options are deprecated, please use Application.init() instead.")}async init(e){e={...e},this.renderer=await mi(e),Ae._plugins.forEach(t=>{t.init.call(this,e)})}render(){this.renderer.render({container:this.stage})}get canvas(){return this.renderer.canvas}get view(){return Ce(we,"Application.view is deprecated, please use Application.canvas instead."),this.renderer.canvas}get screen(){return this.renderer.screen}destroy(e=!1,t=!1){const s=Ae._plugins.slice(0);s.reverse(),s.forEach(i=>{i.destroy.call(this)}),this.stage.destroy(t),this.stage=null,this.renderer.destroy(e),this.renderer=null}};bt._plugins=[];let yt=bt;Me.handleByList(he.Application,yt._plugins);Me.add(vt);class ui extends Kt{constructor(e,t){const{text:s,resolution:i,style:a,anchor:n,width:c,height:o,roundPixels:d,...u}=e;super({...u}),this.batched=!0,this._resolution=null,this._autoResolution=!0,this._didTextUpdate=!0,this._styleClass=t,this.text=s??"",this.style=a,this.resolution=i??null,this.allowChildren=!1,this._anchor=new Zt({_onUpdate:()=>{this.onViewUpdate()}}),n&&(this.anchor=n),this.roundPixels=d??!1,c!==void 0&&(this.width=c),o!==void 0&&(this.height=o)}get anchor(){return this._anchor}set anchor(e){typeof e=="number"?this._anchor.set(e):this._anchor.copyFrom(e)}set text(e){e=e.toString(),this._text!==e&&(this._text=e,this.onViewUpdate())}get text(){return this._text}set resolution(e){this._autoResolution=e===null,this._resolution=e,this.onViewUpdate()}get resolution(){return this._resolution}get style(){return this._style}set style(e){e||(e={}),this._style?.off("update",this.onViewUpdate,this),e instanceof this._styleClass?this._style=e:this._style=new this._styleClass(e),this._style.on("update",this.onViewUpdate,this),this.onViewUpdate()}get width(){return Math.abs(this.scale.x)*this.bounds.width}set width(e){this._setWidth(e,this.bounds.width)}get height(){return Math.abs(this.scale.y)*this.bounds.height}set height(e){this._setHeight(e,this.bounds.height)}getSize(e){return e||(e={}),e.width=Math.abs(this.scale.x)*this.bounds.width,e.height=Math.abs(this.scale.y)*this.bounds.height,e}setSize(e,t){typeof e=="object"?(t=e.height??e.width,e=e.width):t??(t=e),e!==void 0&&this._setWidth(e,this.bounds.width),t!==void 0&&this._setHeight(t,this.bounds.height)}containsPoint(e){const t=this.bounds.width,s=this.bounds.height,i=-t*this.anchor.x;let a=0;return e.x>=i&&e.x<=i+t&&(a=-s*this.anchor.y,e.y>=a&&e.y<=a+s)}onViewUpdate(){this.didViewUpdate||(this._didTextUpdate=!0),super.onViewUpdate()}destroy(e=!1){super.destroy(e),this.owner=null,this._bounds=null,this._anchor=null,(typeof e=="boolean"?e:e?.style)&&this._style.destroy(e),this._style=null,this._text=null}get styleKey(){return`${this._text}:${this._style.styleKey}:${this._resolution}`}}function fi(l,e){let t=l[0]??{};return(typeof t=="string"||l[1])&&(Ce(we,`use new ${e}({ text: "hi!", style }) instead`),t={text:t,style:l[1]}),t}class pi extends ui{constructor(...e){const t=fi(e,"Text");super(t,ts),this.renderPipeId="text",t.textureStyle&&(this.textureStyle=t.textureStyle instanceof Ke?t.textureStyle:new Ke(t.textureStyle))}updateBounds(){const e=this._bounds,t=this._anchor;let s=0,i=0;if(this._style.trim){const{frame:a,canvasAndContext:n}=Qe.getCanvasAndContext({text:this.text,style:this._style,resolution:1});Qe.returnCanvasAndContext(n),s=a.width,i=a.height}else{const a=ss.measureText(this._text,this._style);s=a.width,i=a.height}e.minX=-t._x*s,e.maxX=e.minX+s,e.minY=-t._y*i,e.maxY=e.minY+i}}var vi=P('<div class="level-complete-overlay svelte-1f4jq90"><div class="level-complete-panel svelte-1f4jq90"><div class="level-complete-blur-bg svelte-1f4jq90"></div> <div class="level-complete-content svelte-1f4jq90"><h2 class="level-complete-title svelte-1f4jq90"> </h2> <p class="level-complete-subtitle svelte-1f4jq90"> </p></div></div></div>'),bi=P('<div class="mums-numbers-game svelte-1f4jq90"><div class="game-board-container svelte-1f4jq90"><div class="game-canvas svelte-1f4jq90"></div> <p class="board-time svelte-1f4jq90">Board: <span class="board-timer svelte-1f4jq90"> </span></p></div></div> <!>',1);function yi(l,e){ne(e,!0);let t=L(void 0),s=L(void 0),i=L(null),a=L(0),n=L(1),c=L(0),o=L(3),d=L(1),u=L(0),h=L("00:00"),g=L(at([])),v=L(!1),k=L(null),_=L(!1);class T{app;container;gridSize=5;cellSize=90;canvasSize=this.gridSize*this.cellSize;gridContainer;pathContainer;numbersContainer;highlightContainer;grid=[];solutionPath=[];numberPositions={};playerPath=[];currentPath=[];previewPath=[];isDrawing=!1;isDragging=!1;lastCell=null;currentNumber=1;gameStarted=!1;gameEnded=!1;moveCount=0;timerInterval=null;boardStartTime=null;boardTimerInterval=null;constructor(r){this.container=r,this.app=new yt,this.gridContainer=new le,this.pathContainer=new le,this.numbersContainer=new le,this.highlightContainer=new le,this.app.stage.addChild(this.gridContainer),this.app.stage.addChild(this.pathContainer),this.app.stage.addChild(this.highlightContainer),this.app.stage.addChild(this.numbersContainer),this.initPixi()}async initPixi(){await this.app.init({width:this.canvasSize,height:this.canvasSize,backgroundAlpha:0,antialias:!0}),this.container.appendChild(this.app.canvas),setTimeout(()=>{this.init()},100)}init(){this.initializeGrid(),this.initializeEventListeners(),this.draw(),this.resizeCanvas(),this.setupSocketListeners()}setupSocketListeners(){e.socketClient&&(e.socketClient.addCustomEventListener("initialized",r=>{console.log("Game initialized:",r),r.puzzleState&&(this.loadPuzzleFromServer(r.puzzleState),y(k,r.puzzleState,!0)),r.gameState&&(y(c,r.gameState.score,!0),y(o,r.gameState.lives,!0),w.updateScore(m(c)),w.updateLives(m(o)))}),e.socketClient.addCustomEventListener("started",r=>{console.log("Game started:",r),this.gameStarted=!0,this.boardStartTime=Date.now(),this.startBoardTimer(),y(g,[],!0)}),e.socketClient.addCustomEventListener("action_result",r=>{console.log("Action result:",r),r.actionType==="path_move"&&this.handleServerMoveResult(r.data)}),e.socketClient.addCustomEventListener("ended",r=>{console.log("Game ended:",r),this.handleGameEnd(r)}),e.socketClient.addCustomEventListener("error",r=>{console.error("Game error:",r),y(_,!1),this.handleGameError(r.message||"Unknown game error")}),e.socketClient.addCustomEventListener("game_fatal_error",r=>{console.error("Fatal game error:",r),this.handleFatalError(r.message||"Fatal game error",r.errorType||"fatal")}))}loadPuzzleFromServer(r){this.grid=r.grid,this.numberPositions=r.numberPositions,this.playerPath=r.playerPath||[],this.currentNumber=r.currentNumber||1,this.previewPath=[...this.playerPath],y(n,this.currentNumber,!0),y(a,this.playerPath.length,!0),this.draw()}sendPathToServer(r){if(!e.socketClient||!e.socketClient.isConnected()){console.error("No socket connection available"),this.handleGameError("Connection lost. Please check your internet connection.");return}if(r.length!==0){y(_,!0);try{e.socketClient.sendGameAction("path_move",{path:r,timestamp:Date.now()})}catch(p){console.error("Error sending path to server:",p),this.handleGameError("Failed to send move. Please try again."),y(_,!1)}}}sendMoveToServer(r,p){if(!e.socketClient||!e.socketClient.isConnected()){console.error("No socket connection available"),this.handleGameError("Connection lost. Please check your internet connection.");return}y(_,!0);try{e.socketClient.sendPathMove(r,p)}catch(x){console.error("Error sending move to server:",x),this.handleGameError("Failed to send move. Please try again."),y(_,!1)}}handleServerMoveResult(r){if(y(_,!1),r.isValid)this.playerPath=r.newPath||r.path||[],this.currentNumber=r.currentNumber||1,this.previewPath=[...this.playerPath],y(n,this.currentNumber,!0),y(a,this.playerPath.length,!0),y(c,r.newScore||r.score||m(c),!0),y(o,r.newLives||r.lives||m(o),!0),y(d,r.currentLevel||m(d),!0),y(u,r.levelsCompleted||m(u),!0),w.updateScore(m(c)),w.updateLives(m(o)),r.levelCompleted?r.newPuzzle?this.handleLevelComplete(r):this.handleGameEnd({reason:"time_up",finalScore:m(c),gameWon:!1,levelsCompleted:m(u)}):r.gameEnded&&this.handleGameEnd({reason:r.gameWon?"completed":"no_lives",finalScore:m(c),gameWon:r.gameWon,levelsCompleted:m(u)});else{const p=r.invalidReason||"Invalid path";console.warn("Invalid path:",p),this.showPathError(p),this.isDrawing=!1,this.isDragging=!1,this.previewPath=[...this.playerPath],r.newLives!==void 0&&(y(o,r.newLives,!0),w.updateLives(m(o))),r.gameEnded&&this.handleGameEnd({reason:"no_lives",finalScore:m(c),levelsCompleted:m(u)})}this.draw()}showPathError(r){const p=document.createElement("div");p.className="path-error-message",p.textContent=r,document.body.appendChild(p),setTimeout(()=>p.remove(),1200)}handleGameError(r){console.error("Game error:",r),this.showPathError(r)}handleFatalError(r,p){console.error(`Fatal error (${p}): ${r}`),this.gameEnded=!0,this.stopBoardTimer(),typeof window<"u"&&window.showGameError?window.showGameError(r,p):(console.error("Error modal not available, error:",r),this.showPathError(`Fatal Error: ${r}`))}handleLevelComplete(r){y(v,!0),typeof r.boardTimeMs=="number"&&y(g,[...m(g),r.boardTimeMs],!0),r.newPuzzle&&this.loadPuzzleFromServer(r.newPuzzle),this.boardStartTime=Date.now(),y(h,"00:00"),this.startBoardTimer(),setTimeout(()=>{y(v,!1)},1200),console.log(`Level ${m(d)-1} completed! Starting level ${m(d)}`)}handleGameEnd(r){this.gameEnded=!0,this.stopBoardTimer(),y(c,r.finalScore||m(c),!0),w.updateScore(m(c)),w.endGame(),e.onScoreUpdate&&e.onScoreUpdate(m(c)),e.onGameComplete&&e.onGameComplete(m(c))}initializeGrid(){this.grid=Array(this.gridSize).fill(null).map(()=>Array(this.gridSize).fill(null).map(()=>({visited:!1,number:null,hasPath:!1})))}isValidPosition(r,p){return r>=0&&r<this.gridSize&&p>=0&&p<this.gridSize}resetPlayerState(){this.playerPath=[],this.currentPath=[],this.previewPath=[],this.currentNumber=1,this.lastCell=null,this.gameEnded=!1,this.moveCount=0,this.isDrawing=!1,this.isDragging=!1,y(a,this.moveCount,!0),y(n,this.currentNumber,!0);for(let r=0;r<this.gridSize;r++)for(let p=0;p<this.gridSize;p++)this.grid[r][p]={...this.grid[r][p],visited:!1,hasPath:!1}}initializeEventListeners(){const r=this.app.canvas;r.addEventListener("mousedown",p=>this.handleStart(p)),r.addEventListener("mousemove",p=>this.handleMove(p)),r.addEventListener("mouseup",()=>this.handleEnd()),r.addEventListener("mouseleave",()=>this.handleEnd()),r.addEventListener("touchstart",this.handleTouchStart.bind(this)),r.addEventListener("touchmove",this.handleTouchMove.bind(this)),r.addEventListener("touchend",p=>{p.preventDefault(),this.handleEnd()})}handleTouchStart(r){r.preventDefault(),this.handleStart(r.touches[0])}handleTouchMove(r){r.preventDefault(),this.handleMove(r.touches[0])}handleStart(r){if(this.gameEnded||m(_))return;const p=this.getCellFromEvent(r);p&&(this.gameStarted||this.startGame(),this.isDrawing=!0,this.isDragging=!0,this.previewPath=[...this.playerPath],this.addCellToPreviewPath(p),this.draw())}handleMove(r){if(!this.isDrawing||!this.isDragging||this.gameEnded)return;const p=this.getCellFromEvent(r);p&&(this.addCellToPreviewPath(p),this.draw())}handleEnd(){this.gameEnded||(this.isDrawing=!1,this.isDragging=!1,this.previewPath.length>this.playerPath.length?this.sendPathToServer(this.previewPath):(this.previewPath=[...this.playerPath],this.draw()))}addCellToPreviewPath(r){if(this.previewPath.length>0){const p=this.previewPath[this.previewPath.length-1];if(r.row===p.row&&r.col===p.col)return;const x=this.previewPath.findIndex(H=>H.row===r.row&&H.col===r.col);if(x!==-1){this.previewPath=this.previewPath.slice(0,x+1);return}if(!(Math.abs(r.row-p.row)+Math.abs(r.col-p.col)===1))return}this.previewPath.push(r)}getCellFromEvent(r){const p=this.app.canvas.getBoundingClientRect(),x=this.app.canvas.width/p.width,A=this.app.canvas.height/p.height,H=(r.clientX-p.left)*x,$=(r.clientY-p.top)*A,K=Math.floor(H/this.cellSize),me=Math.floor($/this.cellSize);return this.isValidPosition(me,K)?{row:me,col:K}:null}draw(){this.clearContainers(),this.drawClassicGrid(),this.drawPath(),this.drawNumbers()}clearContainers(){this.gridContainer.removeChildren(),this.pathContainer.removeChildren(),this.numbersContainer.removeChildren(),this.highlightContainer.removeChildren()}drawClassicGrid(){const r=new re;for(let p=0;p<=this.gridSize;p++){const x=p*this.cellSize;r.moveTo(x,0).lineTo(x,this.canvasSize),r.moveTo(0,x).lineTo(this.canvasSize,x)}r.stroke({color:13421772,width:1}),this.gridContainer.addChild(r)}drawPath(){if(this.playerPath.length>=2){const r=new re,p=this.playerPath[0];r.moveTo(p.col*this.cellSize+this.cellSize/2,p.row*this.cellSize+this.cellSize/2);for(let x=1;x<this.playerPath.length;x++){const A=this.playerPath[x];r.lineTo(A.col*this.cellSize+this.cellSize/2,A.row*this.cellSize+this.cellSize/2)}r.stroke({color:2600544,width:8,cap:"round",join:"round"}),this.pathContainer.addChild(r)}if(this.previewPath.length>=2&&this.isDrawing){const r=new re,p=this.previewPath[0];r.moveTo(p.col*this.cellSize+this.cellSize/2,p.row*this.cellSize+this.cellSize/2);for(let x=1;x<this.previewPath.length;x++){const A=this.previewPath[x];r.lineTo(A.col*this.cellSize+this.cellSize/2,A.row*this.cellSize+this.cellSize/2)}r.stroke({color:3447003,width:6,cap:"round",join:"round",alpha:.7}),this.pathContainer.addChild(r)}if(this.previewPath.length===1&&this.isDrawing){const r=this.previewPath[0],p=new re;p.circle(r.col*this.cellSize+this.cellSize/2,r.row*this.cellSize+this.cellSize/2,this.cellSize/4).fill({color:3447003,alpha:.5}),this.pathContainer.addChild(p)}}drawNumbers(){for(let r=1;r<=5;r++){const p=this.numberPositions[r];if(p){const x=p.col*this.cellSize+this.cellSize/2,A=p.row*this.cellSize+this.cellSize/2,H=new re,$=r<this.currentNumber?2600544:15158332;H.circle(x,A,25).fill($),this.numbersContainer.addChild(H);const K=new pi({text:r.toString(),style:{fontFamily:"Arial",fontSize:32,fontWeight:"bold",fill:16777215}});K.anchor.set(.5),K.x=x,K.y=A,this.numbersContainer.addChild(K)}}}highlightCell(r,p,x,A){const H=new re;H.rect(p*this.cellSize+2,r*this.cellSize+2,this.cellSize-4,this.cellSize-4).fill({color:x,alpha:A}),this.highlightContainer.addChild(H)}startGame(){if(e.socketClient&&e.socketClient.isConnected())try{e.socketClient.startGame(),console.log("Start game request sent to server")}catch(r){console.error("Error starting game:",r),this.handleGameError("Failed to start game. Please try again.")}else this.handleGameError("Connection lost. Please check your internet connection.")}startBoardTimer(){this.boardTimerInterval&&clearInterval(this.boardTimerInterval),this.boardTimerInterval=setInterval(()=>{if(!this.gameEnded&&this.boardStartTime){const r=Math.floor((Date.now()-this.boardStartTime)/1e3),p=Math.floor(r/60),x=r%60;y(h,`${p.toString().padStart(2,"0")}:${x.toString().padStart(2,"0")}`)}},1e3)}stopBoardTimer(){this.boardTimerInterval&&clearInterval(this.boardTimerInterval)}resetPath(){y(c,0),w.updateScore(m(c)),this.currentPath=[],this.currentNumber=1,this.draw()}restart(){if(this.gameStarted=!1,this.timerInterval=null,m(o)<=0&&(y(o,3),y(c,0),w.updateLives(m(o)),w.updateScore(m(c))),e.socketClient&&e.socketClient.isConnected())try{e.socketClient.initGame()}catch(r){console.error("Error initializing game:",r),this.handleGameError("Failed to initialize game. Please try again.")}else this.handleGameError("Connection lost. Please check your internet connection.")}resizeCanvas(){if(!this.app.canvas||!m(t))return;const r=Math.min(m(t).clientWidth-40,500);r<500?(this.app.canvas.style.width=r+"px",this.app.canvas.style.height=r+"px"):(this.app.canvas.style.width="500px",this.app.canvas.style.height="500px")}destroy(){if(this.stopBoardTimer(),this.app.canvas){const r=this.app.canvas;r.removeEventListener("mousedown",this.handleStart),r.removeEventListener("mousemove",this.handleMove),r.removeEventListener("mouseup",this.handleEnd),r.removeEventListener("mouseleave",this.handleEnd),r.removeEventListener("touchstart",this.handleTouchStart),r.removeEventListener("touchmove",this.handleTouchMove),r.removeEventListener("touchend",this.handleEnd)}this.app.destroy()}}Oe(()=>{if(m(s)){y(o,3),y(c,0),w.updateLives(m(o)),w.updateScore(m(c)),y(i,new T(m(s)),!0);const D=()=>{m(i)&&m(i).resizeCanvas()};return window.addEventListener("resize",D),()=>{window.removeEventListener("resize",D)}}}),nt(()=>{m(i)&&m(i).destroy()});function C(){m(i)&&!m(i).gameStarted&&m(i).startGame()}var E=bi(),I=Q(E),z=S(I),Y=S(z);ye(Y,D=>y(s,D),()=>m(s));var X=G(Y,2),O=G(S(X)),M=S(O,!0);b(O),b(X),b(z),b(I),ye(I,D=>y(t,D),()=>m(t));var R=G(I,2);{var J=D=>{var r=vi(),p=S(r),x=G(S(p),2),A=S(x),H=S(A);b(A);var $=G(A,2),K=S($);b($),b(x),b(p),b(r),W(()=>{V(H,`Level ${m(d)-1} Complete!`),V(K,`Starting Level ${m(d)??""}...`)}),F(D,r)};j(R,D=>{m(v)&&D(J)})}return W(()=>V(M,m(h))),F(l,E),oe({startGame:C})}class Si{config;container=null;svelteComponent=null;currentScore=0;constructor(e){this.config=e}async init(){if(console.log("Initializing NumberConnect game..."),this.container=document.getElementById(this.config.containerId),!this.container)throw new Error(`Container element with ID "${this.config.containerId}" not found`);this.container.innerHTML="",this.svelteComponent=Bt(yi,{target:this.container,props:{socketClient:this.config.socketClient,gameId:this.config.gameId,containerId:this.config.containerId,onScoreUpdate:e=>{this.currentScore=e,this.config.onScoreUpdate?.(e)},onGameComplete:this.config.onGameComplete}}),console.log("NumberConnect game initialized successfully")}start(){console.log("Starting NumberConnect game..."),this.config.socketClient&&this.config.socketClient.isConnected()?this.config.socketClient.initGame():console.error("No server connection available - cannot initialize game"),this.svelteComponent&&this.svelteComponent.startGame&&this.svelteComponent.startGame()}pause(){console.log("Pausing NumberConnect game...")}resume(){console.log("Resuming NumberConnect game...")}destroy(){console.log("Destroying NumberConnect game..."),this.svelteComponent&&(Pt(this.svelteComponent),this.svelteComponent=null),this.container&&(this.container.innerHTML="")}getCurrentScore(){return this.currentScore}}const N={FACE_HOVER_SCALE:1.1,FACE_CLICK_SCALE:.9,ANIMATION_DURATION:150,TARGET_DISPLAY_SIZE:80,TARGET_DISPLAY_PADDING:10,SCORE_POPUP_DURATION:1e3,SCORE_POPUP_OFFSET:-50},$e={normal_face:{key:"normal_face",path:"/assets-find-someone/faces/normal_face.svg",displayName:"Normal Face"},blue_hat_face:{key:"blue_hat_face",path:"/assets-find-someone/faces/blue_hat_face.svg",displayName:"Blue Hat Face"},frenna_face:{key:"frenna_face",path:"/assets-find-someone/faces/frenna_face.svg",displayName:"Frenna Face"},red_flower_face:{key:"red_flower_face",path:"/assets-find-someone/faces/red_flower_face.svg",displayName:"Red Flower Face"}},et={forest_background:{key:"forest_background",path:"/assets-find-someone/backgrounds/forest.jpg",displayName:"Forest"},city_background:{key:"city_background",path:"/assets-find-someone/backgrounds/city.jpg",displayName:"City"},park_background:{key:"park_background",path:"/assets-find-someone/backgrounds/park.jpg",displayName:"Park"}},tt={target_frame:{key:"target_frame",path:"/assets-find-someone/ui/target_frame.png"},found_checkmark:{key:"found_checkmark",path:"/assets-find-someone/ui/checkmark.png"},timer_bg:{key:"timer_bg",path:"/assets-find-someone/ui/timer_bg.png"}},st={target_found:{key:"target_found",path:"/assets-find-someone/audio/target_found.mp3"},wrong_selection:{key:"wrong_selection",path:"/assets-find-someone/audio/wrong_selection.mp3"},round_complete:{key:"round_complete",path:"/assets-find-someone/audio/round_complete.mp3"},game_music:{key:"game_music",path:"/assets-find-someone/audio/background_music.mp3"}},xe={SUCCESS:9159498,ERROR:16007990,FOUND_HIGHLIGHT:65280};class Ci extends f.Scene{constructor(){super("PreloadScene")}preload(){this.load.on("progress",e=>{w.updateLoadingProgress(e)}),this.load.on("complete",()=>{w.preloadComplete()}),Object.values($e).forEach(e=>{this.load.svg(e.key,e.path)}),Object.values(et).forEach(e=>{this.load.image(e.key,e.path)}),Object.values(tt).forEach(e=>{this.load.svg(e.key,e.path)}),Object.values(st).forEach(e=>{this.load.audio(e.key,e.path)}),this.load.image("particle","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="),this.load.image("button_bg","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==")}create(){console.log("Finding Luigi assets loaded successfully"),this.game.registry.set("faceAssets",$e),this.game.registry.set("backgroundAssets",et),this.game.registry.set("uiAssets",tt),this.game.registry.set("audioAssets",st)}}class wi extends f.Scene{startButton;titleText;instructionsText;constructor(){super("GameStartScene")}create(){const{width:e,height:t}=this.cameras.main;this.add.rectangle(e/2,t/2,e,t,1973790),this.titleText=this.add.text(e/2,t*.2,"Finding Luigi",{fontSize:"48px",color:"#FFD700",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5),this.instructionsText=this.add.text(e/2,t*.4,`Find the target faces as quickly as possible!

• Look for the faces shown at the top
• Tap on them when you find them
• Be careful - wrong taps cost lives!
• Complete all targets to advance`,{fontSize:"20px",color:"#FFFFFF",fontFamily:"Arial, sans-serif",align:"center",lineSpacing:10}).setOrigin(.5),this.startButton=this.add.text(e/2,t*.7,"TAP TO START",{fontSize:"32px",color:"#00FF00",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5),this.startButton.setInteractive({useHandCursor:!0}),this.startButton.on("pointerdown",()=>{this.startGame()}),this.startButton.on("pointerover",()=>{this.startButton.setScale(1.1),this.startButton.setColor("#00FFFF")}),this.startButton.on("pointerout",()=>{this.startButton.setScale(1),this.startButton.setColor("#00FF00")}),this.createFloatingFaces()}createFloatingFaces(){const{width:e,height:t}=this.cameras.main;for(let s=0;s<5;s++){const i=f.Math.Between(50,e-50),a=f.Math.Between(100,t-100),n=this.add.circle(i,a,20,5025616,.3);this.tweens.add({targets:n,y:a-20,duration:2e3+Math.random()*1e3,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"}),this.tweens.add({targets:n,alpha:.1,duration:1500+Math.random()*1e3,yoyo:!0,repeat:-1,ease:"Sine.easeInOut"})}}startGame(){w.initGame(),this.scene.start("GameScene")}}class ki extends f.Scene{socketClient=null;currentRound=null;faceSprites=[];targetDisplays=[];isGameActive=!1;isWaitingForServer=!1;background;scoreText;livesText;timeText;targetsContainer;roundText;roundTimer=null;constructor(){super("GameScene")}init(){this.isGameActive=!1,this.isWaitingForServer=!1,this.currentRound=null,this.faceSprites=[],this.targetDisplays=[];const e=this.registry.get("gameConfig");this.socketClient=e?.socketClient||null,this.setupSocketEventListeners()}setupSocketEventListeners(){this.socketClient&&(this.socketClient.addCustomEventListener("initialized",e=>{console.log("Finding Luigi game initialized by server:",e),e.firstRound&&(console.log("First round data received from server:",e.firstRound),this.currentRound=e.firstRound,this.setupRoundFromServer(e.firstRound)),this.startCountdown()}),this.socketClient.addCustomEventListener("started",e=>{console.log("Finding Luigi game started by server:",e),this.startGame()}),this.socketClient.addCustomEventListener("action_result",e=>{console.log("Action result from server:",e),e.actionType==="face_select"&&this.handleFaceSelectResult(e.data)}),this.socketClient.addCustomEventListener("ended",e=>{console.log("Finding Luigi game ended by server:",e),this.endGame()}),this.socketClient.addCustomEventListener("error",e=>{console.error("Server error:",e),this.showError(e.message||"Unknown server error")}))}create(){this.createUI(),this.time.delayedCall(100,()=>{this.initializeGame()})}createUI(){const{width:e,height:t}=this.cameras.main;this.background=this.add.image(e/2,t/2,"forest_background").setDisplaySize(e,t),this.scoreText=this.add.text(20,20,"Score: 0",{fontSize:"24px",color:"#FFFFFF",fontFamily:"Arial, sans-serif",fontStyle:"bold"}),this.livesText=this.add.text(20,60,"Lives: 3",{fontSize:"24px",color:"#FFFFFF",fontFamily:"Arial, sans-serif",fontStyle:"bold"}),this.timeText=this.add.text(e-20,20,"Time: 30",{fontSize:"24px",color:"#FFFFFF",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(1,0),this.roundText=this.add.text(e/2,20,"Round 1",{fontSize:"28px",color:"#FFD700",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5,0),this.targetsContainer=this.add.container(e/2,120)}initializeGame(){if(!this.socketClient){console.error("No socket client available"),this.showError("Connection error");return}console.log("Initializing Finding Luigi game..."),this.socketClient.initGame()}startCountdown(){console.log("Starting countdown...");const{width:e,height:t}=this.cameras.main,s=this.add.rectangle(e/2,t/2,e,t,0,.7),i=this.add.text(e/2,t/2,"3",{fontSize:"120px",color:"#FFFFFF",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5);let a=3;this.time.addEvent({delay:1e3,repeat:2,callback:()=>{a--,a>0?i.setText(a.toString()):(i.setText("GO!"),this.time.delayedCall(500,()=>{s.destroy(),i.destroy(),this.startGameFromCountdown()}))}})}startGameFromCountdown(){this.socketClient&&(console.log("Starting game from countdown..."),this.socketClient.startGame())}startGame(){console.log("Game started!"),this.isGameActive=!0,this.currentRound&&this.startRoundTimer()}setupRoundFromServer(e){console.log("Setting up round from server:",e),this.clearFaces(),this.roundText.setText(`Round ${e.roundNumber}`),this.createFaceSprites(e.layout.faces),this.createTargetDisplays(e.targets),e.layout.backgroundKey&&this.background.setTexture(e.layout.backgroundKey)}createFaceSprites(e){e.forEach(t=>{const s=this.add.image(t.position.x,t.position.y,t.faceType);s.setScale(t.position.scale),s.setRotation(t.position.rotation*Math.PI/180),s.setDepth(t.zIndex),s.faceData=t,s.isTarget=!1,s.isFound=!1,s.originalScale=t.position.scale,s.originalRotation=t.position.rotation*Math.PI/180,s.setInteractive({useHandCursor:!0}),s.on("pointerdown",()=>{this.handleFaceClick(s)}),s.on("pointerover",()=>{s.isFound||s.setScale(s.originalScale*N.FACE_HOVER_SCALE)}),s.on("pointerout",()=>{s.isFound||s.setScale(s.originalScale)}),this.faceSprites.push(s)})}createTargetDisplays(e){this.targetsContainer.removeAll(!0),this.targetDisplays=[];const t=-(e.length*(N.TARGET_DISPLAY_SIZE+N.TARGET_DISPLAY_PADDING))/2;e.forEach((s,i)=>{const a=t+i*(N.TARGET_DISPLAY_SIZE+N.TARGET_DISPLAY_PADDING),n=this.add.image(a,0,"target_frame").setDisplaySize(N.TARGET_DISPLAY_SIZE,N.TARGET_DISPLAY_SIZE),c=this.add.image(a,0,s).setDisplaySize(N.TARGET_DISPLAY_SIZE*.8,N.TARGET_DISPLAY_SIZE*.8);this.targetsContainer.add([n,c]);const o={faceType:s,sprite:c,frame:n,found:!1};this.targetDisplays.push(o)}),this.faceSprites.forEach(s=>{s.isTarget=e.includes(s.faceData.faceType)})}handleFaceClick(e){!this.isGameActive||this.isWaitingForServer||e.isFound||(console.log("Face clicked:",e.faceData.faceType,e.faceData.id),e.setScale(e.originalScale*N.FACE_CLICK_SCALE),this.time.delayedCall(N.ANIMATION_DURATION,()=>{e.isFound||e.setScale(e.originalScale)}),this.sendFaceSelection(e))}sendFaceSelection(e){if(!this.socketClient)return;this.isWaitingForServer=!0;const t=this.currentRound?Date.now()-this.currentRound.startTime:0;this.socketClient.sendFaceSelect(e.faceData.id,t,{x:e.x,y:e.y})}handleFaceSelectResult(e){this.isWaitingForServer=!1,console.log("Face select result:",e);const t=this.faceSprites.find(s=>s.faceData.id===e.faceId);t&&(e.wasTarget&&e.isCorrect?(this.markFaceAsFound(t),this.markTargetAsFound(e.targetFaceType),this.showScorePopup(t.x,t.y,`+${e.points}`,xe.SUCCESS)):this.showScorePopup(t.x,t.y,`${e.points}`,xe.ERROR)),this.updateScore(e.newScore),this.updateLives(e.newLives),e.gameEnded?this.endGame():e.roundComplete&&e.nextRound&&this.handleRoundComplete(e.nextRound)}markFaceAsFound(e){e.isFound=!0,e.setTint(xe.FOUND_HIGHLIGHT),e.setScale(e.originalScale*1.2);const t=this.add.image(e.x,e.y,"found_checkmark").setDisplaySize(40,40).setDepth(e.depth+1);t.setScale(0),this.tweens.add({targets:t,scale:1,duration:300,ease:"Back.easeOut"})}markTargetAsFound(e){const t=this.targetDisplays.find(s=>s.faceType===e);if(t){t.found=!0;const s=this.add.image(0,0,"found_checkmark").setDisplaySize(30,30);t.checkmark=s,this.targetsContainer.add(s);const i=this.targetDisplays.indexOf(t),a=-(this.targetDisplays.length*(N.TARGET_DISPLAY_SIZE+N.TARGET_DISPLAY_PADDING))/2;s.x=a+i*(N.TARGET_DISPLAY_SIZE+N.TARGET_DISPLAY_PADDING),s.y=0}}showScorePopup(e,t,s,i){const a=this.add.text(e,t,s,{fontSize:"32px",color:`#${i.toString(16).padStart(6,"0")}`,fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5);this.tweens.add({targets:a,y:t+N.SCORE_POPUP_OFFSET,alpha:0,duration:N.SCORE_POPUP_DURATION,ease:"Power2",onComplete:()=>{a.destroy()}})}updateScore(e){this.scoreText.setText(`Score: ${e}`),this.registry.set("currentScore",e)}updateLives(e){this.livesText.setText(`Lives: ${e}`),e<3&&(this.livesText.setColor("#FF0000"),this.time.delayedCall(500,()=>{this.livesText.setColor("#FFFFFF")}))}startRoundTimer(){if(!this.currentRound)return;const e=this.currentRound.timeLimit;let t=Math.floor(e/1e3);this.timeText.setText(`Time: ${t}`),this.roundTimer=this.time.addEvent({delay:1e3,repeat:t-1,callback:()=>{t--,this.timeText.setText(`Time: ${t}`),t<=5&&this.timeText.setColor("#FF0000")}})}handleRoundComplete(e){console.log("Round complete! Next round:",e),this.roundTimer&&(this.roundTimer.destroy(),this.roundTimer=null);const{width:t,height:s}=this.cameras.main,i=this.add.text(t/2,s/2,"ROUND COMPLETE!",{fontSize:"48px",color:"#00FF00",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5);this.time.delayedCall(2e3,()=>{i.destroy(),this.currentRound=e,this.setupRoundFromServer(e),this.startRoundTimer()})}clearFaces(){this.faceSprites.forEach(e=>e.destroy()),this.faceSprites=[]}endGame(){console.log("Game ended!"),this.isGameActive=!1,this.roundTimer&&(this.roundTimer.destroy(),this.roundTimer=null),this.scene.start("GameEndScene")}showError(e){console.error("Game error:",e),typeof window<"u"&&window.showGameError&&window.showGameError(e,"error")}}class Ti extends f.Scene{finalScore=0;playAgainButton;titleText;scoreText;statsText;constructor(){super("GameEndScene")}init(e){this.finalScore=e?.finalScore||this.registry.get("currentScore")||0}create(){const{width:e,height:t}=this.cameras.main;this.add.rectangle(e/2,t/2,e,t,1973790),this.titleText=this.add.text(e/2,t*.15,"GAME OVER",{fontSize:"48px",color:"#FF6B6B",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5),this.scoreText=this.add.text(e/2,t*.3,`Final Score: ${this.finalScore}`,{fontSize:"36px",color:"#FFD700",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5),this.statsText=this.add.text(e/2,t*.45,`Game Statistics:

• Targets Found: --
• Accuracy: --%
• Best Reaction Time: --ms
• Rounds Completed: --`,{fontSize:"20px",color:"#FFFFFF",fontFamily:"Arial, sans-serif",align:"center",lineSpacing:10}).setOrigin(.5),this.playAgainButton=this.add.text(e/2,t*.7,"PLAY AGAIN",{fontSize:"32px",color:"#4CAF50",fontFamily:"Arial, sans-serif",fontStyle:"bold"}).setOrigin(.5),this.playAgainButton.setInteractive({useHandCursor:!0}),this.playAgainButton.on("pointerdown",()=>{this.restartGame()}),this.playAgainButton.on("pointerover",()=>{this.playAgainButton.setScale(1.1),this.playAgainButton.setColor("#66BB6A")}),this.playAgainButton.on("pointerout",()=>{this.playAgainButton.setScale(1),this.playAgainButton.setColor("#4CAF50")}),this.createParticleEffects(),this.animateElementsIn()}createParticleEffects(){const{width:e,height:t}=this.cameras.main;for(let s=0;s<10;s++){const i=this.add.circle(f.Math.Between(0,e),f.Math.Between(t,t+100),f.Math.Between(2,6),5025616,.6);this.tweens.add({targets:i,y:-50,duration:f.Math.Between(3e3,6e3),ease:"Linear",repeat:-1,delay:f.Math.Between(0,2e3),onRepeat:()=>{i.y=t+50,i.x=f.Math.Between(0,e)}}),this.tweens.add({targets:i,x:i.x+f.Math.Between(-50,50),duration:f.Math.Between(2e3,4e3),yoyo:!0,repeat:-1,ease:"Sine.easeInOut"})}}animateElementsIn(){this.titleText.setAlpha(0).setY(this.titleText.y-50),this.scoreText.setAlpha(0).setScale(0),this.statsText.setAlpha(0).setX(this.statsText.x-100),this.playAgainButton.setAlpha(0).setY(this.playAgainButton.y+50),this.tweens.add({targets:this.titleText,alpha:1,y:this.titleText.y+50,duration:800,ease:"Back.easeOut"}),this.time.delayedCall(300,()=>{this.tweens.add({targets:this.scoreText,alpha:1,scale:1,duration:600,ease:"Back.easeOut"})}),this.time.delayedCall(600,()=>{this.tweens.add({targets:this.statsText,alpha:1,x:this.statsText.x+100,duration:600,ease:"Power2.easeOut"})}),this.time.delayedCall(900,()=>{this.tweens.add({targets:this.playAgainButton,alpha:1,y:this.playAgainButton.y-50,duration:600,ease:"Back.easeOut"})})}restartGame(){this.registry.set("currentScore",0),this.scene.start("GameStartScene")}}class _i{config;gameInstance=null;constructor(e){this.config=e}async init(){console.log("Initializing Finding Luigi game...");const e={type:f.AUTO,width:540,height:960,backgroundColor:"#1E1E1E",parent:this.config.containerId,scene:[Ci,wi,ki,Ti],physics:{default:"arcade",arcade:{gravity:{x:0,y:0},debug:!1}},scale:{mode:f.Scale.EXPAND,autoCenter:f.Scale.CENTER_BOTH},render:{antialias:!0,pixelArt:!1,roundPixels:!0,powerPreference:"high-performance"},input:{activePointers:3,windowEvents:!1},dom:{createContainer:!0}};this.gameInstance=new f.Game(e),this.gameInstance&&this.gameInstance.registry.set("gameConfig",this.config)}start(){console.log("Starting Finding Luigi game..."),this.gameInstance?.scene.start("GameScene")}pause(){console.log("Pausing Finding Luigi game..."),this.gameInstance&&this.gameInstance.scene.pause("GameScene")}resume(){console.log("Resuming Finding Luigi game..."),this.gameInstance&&this.gameInstance.scene.resume("GameScene")}destroy(){console.log("Destroying Finding Luigi game..."),this.gameInstance&&(this.gameInstance.destroy(!0),this.gameInstance=null)}getCurrentScore(){return this.gameInstance&&this.gameInstance.registry.get("currentScore")||0}}function xi(l,e,t,s){const i={gameId:l,containerId:e,socketClient:t,...s};switch(l){case"finger-frenzy":return new Ps(i);case"bingo":return new Hs(i);case"matching-mayhem":return new Qs(i);case"numbers":return new ni(i);case"mums-numbers":return new Si(i);case"finding-luigi":return new _i(i);default:throw new Error(`Unknown game ID: ${l}`)}}const Gi=!1,aa=Object.freeze(Object.defineProperty({__proto__:null,ssr:Gi},Symbol.toStringTag,{value:"Module"}));var Ei=P("<!> <!>",1),Li=P('<!> <!> <div class="w-screen h-screen overflow-hidden relative"><!> <div class="w-full h-full box-border" id="game-container"></div> <!> <!> <!> <!></div>',1);function na(l,e){ne(e,!0);const[t,s]=ot(),i=()=>Ee(Ze,"$gameState",t),a=()=>Ee(es,"$opponentState",t);let n=L(!1),c=L("default-room");Ge(()=>{y(c,Rt(Ze)?.roomId??"default-room",!0)});let o=L(void 0),d=L(!1),u=L(!1),h=L(null),g=L(null),v=L(null),k=L(!1),_=L(""),T=L("");const C=q(()=>Xe.params.id);let E=q(()=>Xe.url.searchParams.get("token")),I=L(!1);Ge(()=>{if(y(I,!0),console.log("Game ID:",m(C)),console.log("Token:",m(E)),!m(E)){console.log("No token provided - running in development mode");return}console.log("JWT token received, will be validated by server");const B=m(E);return w.setAuthToken(B),w.setGameId(m(C)),ue.setGameId(m(C)),ue.setConnected(!1),y(h,Jt,!0),(async()=>{try{if(y(g,Le,!0),!m(g)){console.error("Socket client not available");return}if(!B){console.error("No token provided - cannot connect to server");return}await m(g).connect(B,{onScoreUpdate:ee=>{w.updateScore(ee)},onGameComplete:ee=>{w.endGame(),y(u,!0)}}),ue.setConnected(!0),y(v,xi(m(C),"game-container",m(g)),!0),await m(v).init()}catch(ee){console.error("Failed to initialize game:",ee)}})(),()=>{m(g)&&m(g).disconnect(),m(v)&&m(v).destroy(),ue.reset()}});function z(){console.log("Countdown complete"),y(d,!0),w.initGame(),m(v).start()}function Y(B,se="error"){y(_,B,!0),y(T,se,!0),y(k,!0)}function X(){}typeof window<"u"&&(window.showGameError=Y);var O=Li();zt(B=>{W(()=>At.title=`TicTaps - ${m(C)??""}`)});var M=Q(O);Cs(M,{get progress(){return i().loadingProgress}});var R=G(M,2);{var J=B=>{$t(B,{handleStartClick:z,get gameId(){return m(C)}})};j(R,B=>{i().status===ce.Waiting&&B(J)})}var D=G(R,2),r=S(D);{var p=B=>{{let se=q(()=>a().opponent?.score??null),ee=q(()=>a().opponent?.lives??null),Te=q(()=>a().opponent?.name);vs(B,{get score(){return i().score},get time(){return i().time},get totalTime(){return i().totalTime},get lives(){return i().lives},get maxLives(){return i().maxLives},get opponentScore(){return m(se)},get opponentLives(){return m(ee)},get opponentWaiting(){return a().waiting},get opponentName(){return m(Te)}})}};j(r,B=>{i().status===ce.Active&&B(p)})}var x=G(r,2);ye(x,B=>y(o,B),()=>m(o));var A=G(x,2);ys(A,{get show(){return m(d)},duration:3});var H=G(A,2);{let B=q(()=>i().status===ce.Ended);Qt(H,{get show(){return m(B)},get finalScore(){return i().score}})}var $=G(H,2);Ts($,{get isVisible(){return m(k)},get errorMessage(){return m(_)},get errorType(){return m(T)},onClose:X});var K=G($,2);{var me=B=>{var se=Ei(),ee=Q(se);Ls(ee,{onToggle:()=>y(n,!m(n))});var Te=G(ee,2);Gs(Te,{get roomId(){return m(c)},get open(){return m(n)}}),F(B,se)};j(K,B=>{(i().status===ce.Active||i().status===ce.Ended)&&B(me)})}b(D),F(l,O),oe(),s()}export{ft as A,mt as C,gi as R,li as S,pt as V,aa as _,na as a,ri as u};
