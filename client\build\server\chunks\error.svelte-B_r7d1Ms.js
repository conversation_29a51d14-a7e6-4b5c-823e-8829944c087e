import { v as push, T as escape_html, x as pop } from './exports-wEM_yqkf.js';
import { p as page } from './index2-B_5xEZv0.js';
import './state.svelte-DJsENWvz.js';

function Error($$payload, $$props) {
  push();
  $$payload.out.push(`<h1>${escape_html(page.status)}</h1> <p>${escape_html(page.error?.message)}</p>`);
  pop();
}

export { Error as default };
//# sourceMappingURL=error.svelte-B_r7d1Ms.js.map
