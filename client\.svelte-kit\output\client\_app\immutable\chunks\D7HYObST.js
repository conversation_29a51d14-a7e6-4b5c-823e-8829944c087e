import{O as B,ae as C,af as A,ag as L,a0 as j,Q as ee,ah as F,P as te,D as H,w as ne,ai as ae,x as m,aj as N,ak as W,al as P,C as w,B as D,L as v,am as $,an as R,ao as M,y as re,ap as oe,aq as se,ar as ie,as as ue,at as le,au as ce,F as fe,p as de,e as d,av as _e,c as pe,R as S,i as O,aw as he,ax as T,h as ve,ay as be,az as U,a3 as ye,aA as ge,a9 as we,aB as me,aC as Ee,aD as Te,aE as ke,aF as Se,ab as Ce}from"./CBPGQi6i.js";function We(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const Ae=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function $e(e){return Ae.includes(e)}const Le={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function Ue(e){return e=e.toLowerCase(),Le[e]??e}const De=["touchstart","touchmove"];function Oe(e){return De.includes(e)}function Ye(e,t){if(t){const n=document.body;e.autofocus=!0,B(()=>{document.activeElement===n&&e.focus()})}}let z=!1;function xe(){z||(z=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{if(!e.defaultPrevented)for(const t of e.target.elements)t.__on_r?.()})},{capture:!0}))}function Y(e){var t=L,n=j;C(null),A(null);try{return e()}finally{C(t),A(n)}}function Ge(e,t,n,r=n){e.addEventListener(t,()=>Y(n));const o=e.__on_r;o?e.__on_r=()=>{o(),r(!0)}:e.__on_r=()=>r(!0),xe()}const G=new Set,I=new Set;function Me(e,t,n,r={}){function o(a){if(r.capture||E.call(t,a),!a.cancelBubble)return Y(()=>n?.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?B(()=>{t.addEventListener(e,o,r)}):t.addEventListener(e,o,r),o}function Qe(e,t,n,r,o){var a={capture:r,passive:o},l=Me(e,t,n,a);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&te(()=>{t.removeEventListener(e,l,a)})}function Je(e){for(var t=0;t<e.length;t++)G.add(e[t]);for(var n of I)n(e)}function E(e){var t=this,n=t.ownerDocument,r=e.type,o=e.composedPath?.()||[],a=o[0]||e.target,l=0,i=e.__root;if(i){var _=o.indexOf(i);if(_!==-1&&(t===document||t===window)){e.__root=t;return}var s=o.indexOf(t);if(s===-1)return;_<=s&&(l=_)}if(a=o[l]||e.target,a!==t){ee(e,"currentTarget",{configurable:!0,get(){return a||n}});var x=L,p=j;C(null),A(null);try{for(var u,c=[];a!==null;){var b=a.assignedSlot||a.parentNode||a.host||null;try{var h=a["__"+r];if(h!=null&&(!a.disabled||e.target===a))if(F(h)){var[X,...Z]=h;X.apply(a,[e,...Z])}else h.call(a,e)}catch(k){u?c.push(k):u=k}if(e.cancelBubble||b===t||b===null)break;a=b}if(u){for(let k of c)queueMicrotask(()=>{throw k});throw u}}finally{e.__root=t,delete e.currentTarget,C(x),A(p)}}}let f;function Ne(){f=void 0}function Ke(e){let t=null,n=m;var r;if(m){for(t=v,f===void 0&&(f=$(document.head));f!==null&&(f.nodeType!==N||f.data!==W);)f=P(f);f===null?w(!1):f=D(P(f))}m||(r=document.head.appendChild(H()));try{ne(()=>e(r),ae)}finally{n&&(w(!0),f=v,D(t))}}function Xe(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??=e.nodeValue)&&(e.__t=n,e.nodeValue=n+"")}function Q(e,t){return J(e,t)}function Pe(e,t){R(),t.intro=t.intro??!1;const n=t.target,r=m,o=v;try{for(var a=$(n);a&&(a.nodeType!==N||a.data!==W);)a=P(a);if(!a)throw M;w(!0),D(a),re();const l=J(e,{...t,anchor:a});if(v===null||v.nodeType!==N||v.data!==oe)throw se(),M;return w(!1),l}catch(l){if(l===M)return t.recover===!1&&ie(),R(),ue(n),w(!1),Q(e,t);throw l}finally{w(r),D(o),Ne()}}const y=new Map;function J(e,{target:t,anchor:n,props:r={},events:o,context:a,intro:l=!0}){R();var i=new Set,_=p=>{for(var u=0;u<p.length;u++){var c=p[u];if(!i.has(c)){i.add(c);var b=Oe(c);t.addEventListener(c,E,{passive:b});var h=y.get(c);h===void 0?(document.addEventListener(c,E,{passive:b}),y.set(c,1)):y.set(c,h+1)}}};_(le(G)),I.add(_);var s=void 0,x=ce(()=>{var p=n??t.appendChild(H());return fe(()=>{if(a){de({});var u=d;u.c=a}o&&(r.$$events=o),m&&_e(p,null),s=e(p,r)||{},m&&(j.nodes_end=v),a&&pe()}),()=>{for(var u of i){t.removeEventListener(u,E);var c=y.get(u);--c===0?(document.removeEventListener(u,E),y.delete(u)):y.set(u,c)}I.delete(_),p!==n&&p.parentNode?.removeChild(p)}});return V.set(s,x),s}let V=new WeakMap;function Re(e,t){const n=V.get(e);return n?(V.delete(e),n(t)):Promise.resolve()}function Ie(e,t,n){if(e==null)return t(void 0),S;const r=O(()=>e.subscribe(t,n));return r.unsubscribe?()=>r.unsubscribe():r}const g=[];function Ze(e,t=S){let n=null;const r=new Set;function o(i){if(he(e,i)&&(e=i,n)){const _=!g.length;for(const s of r)s[1](),g.push(s,e);if(_){for(let s=0;s<g.length;s+=2)g[s][0](g[s+1]);g.length=0}}}function a(i){o(i(e))}function l(i,_=S){const s=[i,_];return r.add(s),r.size===1&&(n=t(o,a)||S),i(e),()=>{r.delete(s),r.size===0&&n&&(n(),n=null)}}return{set:o,update:a,subscribe:l}}function et(e){let t;return Ie(e,n=>t=n)(),t}function Ve(){return L===null&&be(),(L.ac??=new AbortController).signal}function K(e){d===null&&T(),ye&&d.l!==null?q(d).m.push(e):ve(()=>{const t=O(e);if(typeof t=="function")return t})}function je(e){d===null&&T(),K(()=>()=>O(e))}function qe(e,t,{bubbles:n=!1,cancelable:r=!1}={}){return new CustomEvent(e,{detail:t,bubbles:n,cancelable:r})}function ze(){const e=d;return e===null&&T(),(t,n,r)=>{const o=e.s.$$events?.[t];if(o){const a=F(o)?o.slice():[o],l=qe(t,n,r);for(const i of a)i.call(e.x,l);return!l.defaultPrevented}return!0}}function Be(e){d===null&&T(),d.l===null&&U(),q(d).b.push(e)}function Fe(e){d===null&&T(),d.l===null&&U(),q(d).a.push(e)}function q(e){var t=e.l;return t.u??={a:[],b:[],m:[]}}const tt=Object.freeze(Object.defineProperty({__proto__:null,afterUpdate:Fe,beforeUpdate:Be,createEventDispatcher:ze,createRawSnippet:ge,flushSync:we,getAbortSignal:Ve,getAllContexts:me,getContext:Ee,hasContext:Te,hydrate:Pe,mount:Q,onDestroy:je,onMount:K,setContext:ke,settled:Se,tick:Ce,unmount:Re,untrack:O},Symbol.toStringTag,{value:"Module"}));export{Ie as a,Pe as b,je as c,xe as d,Qe as e,Me as f,et as g,Ke as h,We as i,Je as j,Ye as k,$e as l,Q as m,Ue as n,K as o,Ge as p,tt as q,Xe as s,Re as u,Ze as w};
