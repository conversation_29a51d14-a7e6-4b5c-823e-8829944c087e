<svg width="540" height="960" viewBox="0 0 540 960" xmlns="http://www.w3.org/2000/svg">
  <!-- Forest background with trees and foliage -->
  <defs>
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#98FB98;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="treeGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#228B22;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#006400;stop-opacity:1" />
    </radialGradient>
  </defs>
  
  <!-- Sky background -->
  <rect width="540" height="960" fill="url(#skyGradient)"/>
  
  <!-- Ground -->
  <rect x="0" y="800" width="540" height="160" fill="#8B4513"/>
  
  <!-- Trees in background -->
  <ellipse cx="100" cy="700" rx="60" ry="80" fill="url(#treeGradient)" opacity="0.7"/>
  <rect x="85" y="700" width="30" height="100" fill="#8B4513"/>
  
  <ellipse cx="300" cy="650" rx="80" ry="100" fill="url(#treeGradient)" opacity="0.6"/>
  <rect x="280" y="650" width="40" height="150" fill="#8B4513"/>
  
  <ellipse cx="450" cy="720" rx="50" ry="70" fill="url(#treeGradient)" opacity="0.8"/>
  <rect x="440" y="720" width="20" height="80" fill="#8B4513"/>
  
  <!-- Bushes and foliage -->
  <ellipse cx="50" cy="850" rx="40" ry="30" fill="#32CD32"/>
  <ellipse cx="200" cy="870" rx="60" ry="40" fill="#228B22"/>
  <ellipse cx="400" cy="860" rx="50" ry="35" fill="#32CD32"/>
  <ellipse cx="500" cy="880" rx="30" ry="25" fill="#228B22"/>
  
  <!-- Scattered leaves for texture -->
  <circle cx="150" cy="400" r="3" fill="#228B22" opacity="0.6"/>
  <circle cx="320" cy="350" r="2" fill="#32CD32" opacity="0.5"/>
  <circle cx="80" cy="500" r="2.5" fill="#228B22" opacity="0.7"/>
  <circle cx="420" cy="450" r="2" fill="#32CD32" opacity="0.6"/>
  <circle cx="250" cy="600" r="3" fill="#228B22" opacity="0.5"/>
</svg>
