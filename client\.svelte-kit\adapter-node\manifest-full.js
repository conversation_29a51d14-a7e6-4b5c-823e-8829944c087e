export const manifest = (() => {
function __memo(fn) {
	let value;
	return () => value ??= (value = fn());
}

return {
	appDir: "_app",
	appPath: "_app",
	assets: new Set(["assets/audio/click.mp3","assets/audio/click.ogg","assets/audio/click.wav","assets/audio/countdown.mp3","assets/audio/countdown.ogg","assets/audio/countdown.wav","assets/audio/go.mp3","assets/audio/go.wav","assets/audio/wrong.mp3","assets/audio/wrong.ogg","assets/audio/wrong.wav","assets/images/back_to_lobby.png","assets/images/button_bg.svg","assets/images/countdown-1.png","assets/images/countdown-2.png","assets/images/countdown-3.png","assets/images/countdown-go.png","assets/images/counter/1.svg","assets/images/counter/2.svg","assets/images/counter/3.svg","assets/images/counter/GO.svg","assets/images/game_bg.png","assets/images/game_over.svg","assets/images/game_start.png","assets/images/mdi--heart-broken.svg","assets/images/mdi--heart.svg","assets/images/mdi-light--heart.svg","assets/images/timer_bg.svg","assets/images/timer_countdown_bg.png","assets/images/timer_icon.png","assets-bingo/audio/match.mp3","assets-bingo/audio/match.ogg","assets-bingo/audio/match.wav","assets-bingo/audio/number_appear.mp3","assets-bingo/audio/number_appear.ogg","assets-bingo/audio/number_appear.wav","assets-bingo/audio/win.mp3","assets-bingo/audio/win.ogg","assets-bingo/audio/win.wav","assets-bingo/fonts/fonts.css","assets-bingo/fonts/TTNeorisBold.ttf","assets-bingo/images/game_name.png","assets-bingo/images/game_name.svg","assets-find-someone/backgrounds/city.svg","assets-find-someone/backgrounds/forest.svg","assets-find-someone/backgrounds/park.svg","assets-find-someone/faces/blue_hat_face.svg","assets-find-someone/faces/frenna_face.svg","assets-find-someone/faces/normal_face.svg","assets-find-someone/faces/red_flower_face.svg","assets-find-someone/ui/checkmark.svg","assets-find-someone/ui/target_frame.svg","assets-finger-frenzy/images/block_active.png","assets-finger-frenzy/images/block_inactive.png","assets-finger-frenzy/images/block_inactive.svg","assets-finger-frenzy/images/game_name.png","assets-finger-frenzy/sounds/right.mp3","assets-finger-frenzy/sounds/right.ogg","assets-finger-frenzy/sounds/right.wav","assets-finger-frenzy/sounds/tap.mp3","assets-finger-frenzy/sounds/tap.ogg","assets-finger-frenzy/sounds/tap.wav","assets-finger-frenzy/sounds/timeout.mp3","assets-finger-frenzy/sounds/timeout.ogg","assets-finger-frenzy/sounds/timeout.wav","assets-matching-mayhem/fonts/font.png","assets-matching-mayhem/fonts/font.xml","assets-matching-mayhem/fonts/montserrat.ttf","assets-matching-mayhem/fonts/neoris_bold.ttf","assets-matching-mayhem/images/0/0.png","assets-matching-mayhem/images/0/1.png","assets-matching-mayhem/images/0/2.png","assets-matching-mayhem/images/0/3.png","assets-matching-mayhem/images/1/0.png","assets-matching-mayhem/images/1/1.png","assets-matching-mayhem/images/1/2.png","assets-matching-mayhem/images/1/3.png","assets-matching-mayhem/images/2/0.png","assets-matching-mayhem/images/2/1.png","assets-matching-mayhem/images/2/2.png","assets-matching-mayhem/images/2/3.png","assets-matching-mayhem/images/card_bg.svg","assets-matching-mayhem/images/card_correct_bg.png","assets-matching-mayhem/images/card_incorrect_bg.png","assets-matching-mayhem/images/game_name.png","assets-matching-mayhem/images/game_name.svg","assets-matching-mayhem/sounds/correct.mp3","assets-matching-mayhem/sounds/correct.wav","assets-matching-mayhem/sounds/end.mp3","assets-matching-mayhem/sounds/end.wav","assets-matching-mayhem/sounds/laser.mp3","assets-matching-mayhem/sounds/laser.wav","assets-matching-mayhem/sounds/round.mp3","assets-matching-mayhem/sounds/round.wav","assets-numbers/fonts/TT Neoris Trial Bold.ttf","assets-numbers/images/circle.png","assets-numbers/images/game_name.png","assets-numbers/images/game_name.svg","assets-numbers/sounds/collect.mp3","assets-numbers/sounds/collect.ogg","assets-numbers/sounds/complete.mp3","assets-numbers/sounds/complete.ogg","assets-numbers/sounds/error.mp3","assets-numbers/sounds/error.ogg","assets-numbers/sounds/timeout.mp3","assets-numbers/sounds/timeout.ogg","favicon.svg"]),
	mimeTypes: {".mp3":"audio/mpeg",".ogg":"audio/ogg",".wav":"audio/wav",".png":"image/png",".svg":"image/svg+xml",".css":"text/css",".ttf":"font/ttf",".xml":"text/xml"},
	_: {
		client: {start:"_app/immutable/entry/start.D0EQJ1wS.js",app:"_app/immutable/entry/app.B43N_9Fw.js",imports:["_app/immutable/entry/start.D0EQJ1wS.js","_app/immutable/chunks/CbN9a2Ga.js","_app/immutable/chunks/D7HYObST.js","_app/immutable/chunks/CBPGQi6i.js","_app/immutable/entry/app.B43N_9Fw.js","_app/immutable/chunks/6c5zsAwx.js","_app/immutable/chunks/CBPGQi6i.js","_app/immutable/chunks/D7HYObST.js","_app/immutable/chunks/DsnmJJEf.js"],stylesheets:[],fonts:[],uses_env_dynamic_public:true},
		nodes: [
			__memo(() => import('./nodes/0.js')),
			__memo(() => import('./nodes/1.js')),
			__memo(() => import('./nodes/2.js')),
			__memo(() => import('./nodes/3.js'))
		],
		routes: [
			{
				id: "/",
				pattern: /^\/$/,
				params: [],
				page: { layouts: [0,], errors: [1,], leaf: 2 },
				endpoint: null
			},
			{
				id: "/game/[id]",
				pattern: /^\/game\/([^/]+?)\/?$/,
				params: [{"name":"id","optional":false,"rest":false,"chained":false}],
				page: { layouts: [0,], errors: [1,], leaf: 3 },
				endpoint: null
			}
		],
		prerendered_routes: new Set([]),
		matchers: async () => {
			
			return {  };
		},
		server_assets: {}
	}
}
})();
