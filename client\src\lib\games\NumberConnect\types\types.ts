/**
 * NumberConnect Game Types
 * Client-side types for the NumberConnect game
 */

// ===== POSITION AND GEOMETRY TYPES =====

/**
 * 2D grid position coordinates (row, col)
 */
export interface Position {
  row: number;
  col: number;
}

/**
 * Grid cell data structure
 */
export interface GridCell {
  visited: boolean;
  number: number | null;
  hasPath: boolean;
}

/**
 * Generated level data structure
 */
export interface GeneratedLevel {
  grid: GridCell[][];
  solutionPath: Position[];
  numberPositions: { [key: number]: Position };
  maxNumber: number;
}

// ===== SHARED TYPES =====

/**
 * Message types sent to TicTaps platform
 */
export type TicTapsMessageType = 'gameReady' | 'gameScore' | 'gameQuit';

/**
 * Message structure for TicTaps communication
 */
export interface TicTapsMessage {
  type: TicTapsMessageType;
  score?: number;
  data?: any;
}

/**
 * Configuration for score display and behavior
 */
export interface ScoreConfig {
  initialScore?: number;
  fontFamily?: string;
  fontSize?: string;
  labelFontSize?: string;
  scoreColor?: string;
  labelColor?: string;
  animationColor?: string;
  animationDuration?: number;
}

/**
 * Configuration for lives display and behavior
 */
export interface LivesConfig {
  initialLives?: number;
}

/**
 * Configuration for score animation
 */
export interface ScoreAnimationConfig {
  startX: number;
  startY: number;
  points: number;
  duration?: number;
  color?: string;
}
