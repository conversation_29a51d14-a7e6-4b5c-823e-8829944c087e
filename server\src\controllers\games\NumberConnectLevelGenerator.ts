/**
 * Server-side Level Generator for Number Connect (Mums Numbers)
 * Mirrors the client generator to keep solvability consistent.
 */

import type { Position, GridCell, GeneratedLevel } from '../../types/numberConnect.js';

export class LevelGenerator {
  private gridSize: number;
  private maxNumber: number;
  private rngInt: (max: number) => number;

  constructor(gridSize: number = 5, maxNumber: number = 5, rngInt?: (max: number) => number) {
    this.gridSize = gridSize;
    this.maxNumber = maxNumber;
    this.rngInt = rngInt ?? ((max: number) => Math.floor(Math.random() * max));
  }

  generateSolvablePuzzle(): GeneratedLevel {
    let attempts = 0;
    const maxAttempts = 1000;

    while (attempts < maxAttempts) {
      attempts++;

      // Random starting position
      const start = {
        row: this.rngInt(this.gridSize),
        col: this.rngInt(this.gridSize),
      };

      // Find a Hamiltonian-like path that covers all cells
      const path = this.findCompletePath(start, {}, [start]);

      if (path && path.length === this.gridSize * this.gridSize) {
        const numberPositions = this.placeNumbers(path);
        if (this.validateSolution(path, numberPositions)) {
          const grid = this.initializeGrid();
          this.populateGridWithNumbers(grid, numberPositions);
          return {
            grid,
            solutionPath: path,
            numberPositions,
            maxNumber: this.maxNumber,
          };
        }
      }
    }

    // Fallback if complex method fails
    return this.generateFallbackPuzzle();
  }

  private initializeGrid(): GridCell[][] {
    return Array(this.gridSize)
      .fill(null)
      .map(() =>
        Array(this.gridSize)
          .fill(null)
          .map(() => ({ visited: false, number: null, hasPath: false }))
      );
  }

  private getNeighbors(row: number, col: number): Position[] {
    const n: Position[] = [];
    if (row > 0) n.push({ row: row - 1, col });
    if (row < this.gridSize - 1) n.push({ row: row + 1, col });
    if (col > 0) n.push({ row, col: col - 1 });
    if (col < this.gridSize - 1) n.push({ row, col: col + 1 });
    return n;
  }

  private shuffleArray<T>(array: T[]): T[] {
    const a = [...array];
    for (let i = a.length - 1; i > 0; i--) {
      const j = this.rngInt(i + 1);
      [a[i], a[j]] = [a[j], a[i]];
    }
    return a;
  }

  private findCompletePath(
    currentPos: Position,
    visited: { [key: string]: boolean },
    path: Position[]
  ): Position[] | null {
    if (path.length === this.gridSize * this.gridSize) {
      return path;
    }

    const key = `${currentPos.row}-${currentPos.col}`;
    visited[key] = true;

    const neighbors = this.shuffleArray(
      this.getNeighbors(currentPos.row, currentPos.col)
    );

    for (const neighbor of neighbors) {
      const nKey = `${neighbor.row}-${neighbor.col}`;
      if (!visited[nKey]) {
        const res = this.findCompletePath(neighbor, { ...visited }, [...path, neighbor]);
        if (res) return res;
      }
    }

    return null;
  }

  // Place numbers evenly along the path (mirrors client logic)
  private placeNumbers(path: Position[]): { [key: number]: Position } {
    const numberPositions: { [key: number]: Position } = {};
    const L = path.length;

    // Evenly spaced indices from start to end inclusive
    for (let i = 0; i < this.maxNumber; i++) {
      const idx = Math.round((i * (L - 1)) / (this.maxNumber - 1));
      numberPositions[i + 1] = path[Math.max(0, Math.min(L - 1, idx))];
    }

    return numberPositions;
  }

  private populateGridWithNumbers(
    grid: GridCell[][],
    numberPositions: { [key: number]: Position }
  ): void {
    for (let n = 1; n <= this.maxNumber; n++) {
      const pos = numberPositions[n];
      if (pos) {
        grid[pos.row][pos.col].number = n;
      }
    }
  }

  private generateFallbackPuzzle(): GeneratedLevel {
    const grid = this.initializeGrid();
    const path: Position[] = [];

    for (let r = 0; r < this.gridSize; r++) {
      if (r % 2 === 0) {
        for (let c = 0; c < this.gridSize; c++) path.push({ row: r, col: c });
      } else {
        for (let c = this.gridSize - 1; c >= 0; c--) path.push({ row: r, col: c });
      }
    }

    const numberPositions = this.placeNumbers(path);
    this.populateGridWithNumbers(grid, numberPositions);

    // Sanity check
    if (!this.validateSolution(path, numberPositions)) {
      // Still return it; should not happen with even spacing
      // but keep behavior consistent with client
    }

    return {
      grid,
      solutionPath: path,
      numberPositions,
      maxNumber: this.maxNumber,
    };
  }

  // Match client validation closely
  validateSolution(
    path: Position[],
    numberPositions: { [key: number]: Position }
  ): boolean {
    if (path.length !== this.gridSize * this.gridSize) return false;

    const visited = new Set<string>();
    for (const pos of path) {
      const k = `${pos.row}-${pos.col}`;
      if (visited.has(k)) return false;
      visited.add(k);
    }

    for (let r = 0; r < this.gridSize; r++) {
      for (let c = 0; c < this.gridSize; c++) {
        if (!visited.has(`${r}-${c}`)) return false;
      }
    }

    let expected = 1;
    const found: number[] = [];

    for (const pos of path) {
      for (let n = 1; n <= this.maxNumber; n++) {
        const np = numberPositions[n];
        if (np && np.row === pos.row && np.col === pos.col) {
          found.push(n);
          if (n === expected) expected++;
          break;
        }
      }
    }

    return found.length === this.maxNumber && expected > this.maxNumber;
  }
}

