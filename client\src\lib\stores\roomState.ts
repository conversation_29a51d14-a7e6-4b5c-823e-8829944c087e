import { writable } from 'svelte/store';

export interface Player {
  userId: string;
  name?: string;
  displayName?: string;
  isHost: boolean;
  isReady: boolean;
  isConnected: boolean;
  joinedAt: Date;
}

export enum RoomStatus {
  NotInRoom = 'not_in_room',
  Joining = 'joining',
  InRoom = 'in_room',
  Starting = 'starting',
  GameActive = 'game_active',
  Leaving = 'leaving',
  Error = 'error'
}

export interface RoomState {
  status: RoomStatus;
  roomId: string | null;
  roomCode: string | null; // Human-readable room code for sharing
  players: Player[];
  currentUserId: string | null;
  isHost: boolean;
  hostUserId: string | null;
  maxPlayers: number;
  gameId: string | null;
  isConnected: boolean;
  error: string | null;
  isLoading: boolean;
  loadingMessage: string | null;
}

const initialState: RoomState = {
  status: RoomStatus.NotInRoom,
  roomId: null,
  roomCode: null,
  players: [],
  currentUserId: null,
  isHost: false,
  hostUserId: null,
  maxPlayers: 2, // Default max players
  gameId: null,
  isConnected: false,
  error: null,
  isLoading: false,
  loadingMessage: null
};

export const roomState = writable<RoomState>(initialState);

export const roomActions = {
  // Reset room state to initial state
  reset: () => {
    console.log('[RoomState] Resetting room state');
    roomState.set(initialState);
  },

  // Set loading state
  setLoading: (isLoading: boolean, message?: string) => {
    console.log('[RoomState] Setting loading state:', isLoading, message);
    roomState.update(state => ({
      ...state,
      isLoading,
      loadingMessage: message || null
    }));
  },

  // Set error state
  setError: (error: string | null) => {
    console.log('[RoomState] Setting error:', error);
    roomState.update(state => ({
      ...state,
      error,
      status: error ? RoomStatus.Error : state.status,
      isLoading: false,
      loadingMessage: null
    }));
  },

  // Clear error
  clearError: () => {
    console.log('[RoomState] Clearing error');
    roomState.update(state => ({
      ...state,
      error: null,
      status: state.status === RoomStatus.Error ? RoomStatus.NotInRoom : state.status
    }));
  },

  // Set room connection status
  setConnected: (isConnected: boolean) => {
    console.log('[RoomState] Setting connection status:', isConnected);
    roomState.update(state => ({
      ...state,
      isConnected
    }));
  },

  // Start joining a room
  startJoining: (roomCode: string) => {
    console.log('[RoomState] Starting to join room:', roomCode);
    roomState.update(state => ({
      ...state,
      status: RoomStatus.Joining,
      roomCode,
      error: null,
      isLoading: true,
      loadingMessage: 'Joining room...'
    }));
  },

  // Successfully joined a room
  joinedRoom: (roomData: {
    roomId: string;
    roomCode: string;
    players: Player[];
    hostUserId: string;
    currentUserId: string;
    gameId: string;
    maxPlayers?: number;
  }) => {
    console.log('[RoomState] Successfully joined room:', roomData);
    const isHost = roomData.currentUserId === roomData.hostUserId;
    
    roomState.update(state => ({
      ...state,
      status: RoomStatus.InRoom,
      roomId: roomData.roomId,
      roomCode: roomData.roomCode,
      players: roomData.players,
      currentUserId: roomData.currentUserId,
      isHost,
      hostUserId: roomData.hostUserId,
      gameId: roomData.gameId,
      maxPlayers: roomData.maxPlayers || state.maxPlayers,
      isConnected: true,
      error: null,
      isLoading: false,
      loadingMessage: null
    }));
  },

  // Update player list (replaces entire list - for room_players_update events)
  updatePlayers: (players: Player[]) => {
    console.log('[RoomState] Updating entire player list:', players);
    roomState.update(state => ({
      ...state,
      players: [...players] // Create a new array to ensure reactivity
    }));
  },

  // Add a player to the room
  addPlayer: (player: Player) => {
    console.log('[RoomState] Adding player:', player);
    roomState.update(state => ({
      ...state,
      players: [...state.players.filter(p => p.userId !== player.userId), player]
    }));
  },

  // Remove a player from the room
  removePlayer: (userId: string) => {
    console.log('[RoomState] Removing player:', userId);
    roomState.update(state => ({
      ...state,
      players: state.players.filter(p => p.userId !== userId)
    }));
  },

  // Update host
  updateHost: (hostUserId: string, currentUserId: string) => {
    console.log('[RoomState] Updating host:', hostUserId);
    roomState.update(state => ({
      ...state,
      hostUserId,
      isHost: currentUserId === hostUserId,
      players: state.players.map(player => ({
        ...player,
        isHost: player.userId === hostUserId
      }))
    }));
  },

  // Update player ready status
  updatePlayerReady: (userId: string, isReady: boolean) => {
    console.log('[RoomState] Updating player ready status:', userId, isReady);
    roomState.update(state => ({
      ...state,
      players: state.players.map(player =>
        player.userId === userId ? { ...player, isReady } : player
      )
    }));
  },

  // Update player connection status
  updatePlayerConnection: (userId: string, isConnected: boolean) => {
    console.log('[RoomState] Updating player connection:', userId, isConnected);
    roomState.update(state => ({
      ...state,
      players: state.players.map(player =>
        player.userId === userId ? { ...player, isConnected } : player
      )
    }));
  },

  // Start leaving room
  startLeaving: () => {
    console.log('[RoomState] Starting to leave room');
    roomState.update(state => ({
      ...state,
      status: RoomStatus.Leaving,
      isLoading: true,
      loadingMessage: 'Leaving room...'
    }));
  },

  // Successfully left room
  leftRoom: () => {
    console.log('[RoomState] Successfully left room');
    roomState.set({
      ...initialState,
      isConnected: true // Maintain socket connection
    });
  },

  // Start game (host action)
  startGame: () => {
    console.log('[RoomState] Starting game');
    roomState.update(state => ({
      ...state,
      status: RoomStatus.Starting,
      isLoading: true,
      loadingMessage: 'Starting game...'
    }));
  },

  // Game started successfully
  gameStarted: () => {
    console.log('[RoomState] Game started');
    roomState.update(state => ({
      ...state,
      status: RoomStatus.GameActive,
      isLoading: false,
      loadingMessage: null
    }));
  },

  // Set current user ID
  setCurrentUserId: (userId: string) => {
    console.log('[RoomState] Setting current user ID:', userId);
    roomState.update(state => ({
      ...state,
      currentUserId: userId
    }));
  },

  // Set game ID
  setGameId: (gameId: string) => {
    console.log('[RoomState] Setting game ID:', gameId);
    roomState.update(state => ({
      ...state,
      gameId
    }));
  }
};
