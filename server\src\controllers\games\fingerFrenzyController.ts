import type { GameService } from '../../services/gameService';
import { logger } from '../../utils/logger';
import type { Server, Socket } from 'socket.io';
import type { GameAction } from '../../types/game';
import { FINGER_FRENZY_CONFIG } from '../../utils/fingerFrenzyConstants';
import { GAME_TYPES } from '../../utils/constants';
import type {
  GridBlock,
  FingerFrenzyGameData,
  TileTapResult,
  GridState,
  TileStates,
  TileTapActionData
} from '../../types/fingerFrenzy';

import type {
  GameInitResult,
  EndReason,
  GameStartData,
  GameEndData,
  GameActionData
} from '../../types/game';
import { submitGameScore, ScoreSubmissionData } from '../../utils/externalApi';
import { sessionService } from '../../services/sessionService';
import { getAuthenticatedUser, AuthenticatedSocket } from '../../middleware/auth';

export default class FingerFrenzyController {
  private gameService: GameService;
  private gameData: Map<string, FingerFrenzyGameData> = new Map();
  private sessionData: Map<string, { submitScoreId?: string; authToken?: string }> = new Map();
  private socketMap: Map<string, Socket> = new Map();

  constructor(gameService: GameService) {
    this.gameService = gameService;
  }

  /**
   * Initialize a new Finger Frenzy game session (called at client load)
   */
  initializeGame(roomId: string, socket: Socket, userId?: string): GameInitResult {
    // Check if room already has a game state
    let gameState = this.gameService.getGameState(roomId, userId);

    if (gameState) {
      // Game state exists, check if we can initialize
      if (gameState.status === 'active') {
        return { success: false, message: 'Game is already active' };
      }
      // If game ended, clean up and create new state to allow restart
      if (gameState.status === 'ended') {
        this.cleanupGame(roomId);
        gameState = this.gameService.createGameState(roomId, GAME_TYPES.FINGER_FRENZY, FINGER_FRENZY_CONFIG.MAX_LIVES, userId);
      }
    } else {
      // Create new game state
      gameState = this.gameService.createGameState(roomId, GAME_TYPES.FINGER_FRENZY, FINGER_FRENZY_CONFIG.MAX_LIVES, userId);
    }

    // Create and initialize game data with grid (but don't activate blocks yet)
    const grid = this.createGrid();
    const gameData: FingerFrenzyGameData = {
      grid,
      activeBlockCount: 0
    };

    // Use per-player key to isolate game data
    const key = `${roomId}:${userId ?? ''}`;
    this.gameData.set(key, gameData);

    // Store socket reference for game events
    this.socketMap.set(key, socket);

    logger.info(`Finger Frenzy game initialized for room ${roomId}`);

    return { success: true, gameState };
  }

  /**
   * Start the game (called after countdown ends)
   */
  startGame(roomId: string, socket: Socket, userId?: string): GameInitResult {
    const gameState = this.gameService.getGameState(roomId, userId);
    const key = `${roomId}:${userId ?? ''}`;
    const gameData = this.gameData.get(key);

    if (!gameState) {
      return { success: false, message: 'Game not initialized. Call initializeGame first.' };
    }

    if (!gameData) {
      return { success: false, message: 'Game data not found. Call initializeGame first.' };
    }

    if (gameState.status === 'active') {
      return { success: false, message: 'Game is already active' };
    }

    // If game ended, clean up and allow restart
    if (gameState.status === 'ended') {
      this.cleanupGame(roomId);
      return { success: false, message: 'Game ended, please reinitialize' };
    }

    // Start the game using GameService
    const startSuccess = this.gameService.startGame(roomId, socket, userId);
    if (!startSuccess) {
      return { success: false, message: 'Failed to start game' };
    }

    // Activate initial blocks for game start
    this.activateInitialBlocks(roomId, userId);

    logger.info(`Finger Frenzy game started for room ${roomId}`);

    return { success: true, gameState };
  }

  /**
   * Initialize and start a new Finger Frenzy game session (legacy method for backward compatibility)
   * @deprecated Use initializeGame() followed by startGame() instead
   */
  initializeAndStartGame(roomId: string, socket: Socket): GameInitResult {
    const initResult = this.initializeGame(roomId, socket);
    if (!initResult.success) {
      return initResult;
    }

    return this.startGame(roomId, socket);
  }

  /**
   * End the game session
   */
   async endGame(roomId: string, reason: EndReason = 'manual', userId?: string): Promise<boolean> {
    const gameState = this.gameService.getGameState(roomId, userId);
    const socket = this.socketMap.get(roomId);

    if (!gameState || gameState.status !== 'active') {
      logger.warn(`Cannot end game: no active game in room ${roomId}`);
      return false;
    }

    // End game using GameService
    this.gameService.endGame(roomId, reason, userId);

    // Submit score to external API using session service
    const sessionInfo = sessionService.getGameSessionData(roomId);
    await this.submitScore(roomId, gameState, reason, sessionInfo?.submitScoreId, sessionInfo?.authToken);

    // Emit ended event to client if socket is available
    if (socket) {
      socket.emit('ended', {
        reason,
        finalScore: gameState?.score || 0
      });
    }

    // Deactivate all blocks
    this.deactivateAllBlocks(roomId, userId);

    // Clean up game state
    this.cleanupGame(roomId, userId);

    logger.info(`Finger Frenzy game ended in room ${roomId}, reason: ${reason}, final score: ${gameState.score}`);
    return true;
  }

  /**
   * Create a 4x4 grid of blocks
   */
  createGrid(): GridBlock[] {
    const grid: GridBlock[] = [];

    for (let row = 0; row < FINGER_FRENZY_CONFIG.GRID_SIZE; row++) {
      for (let col = 0; col < FINGER_FRENZY_CONFIG.GRID_SIZE; col++) {
        const index = row * FINGER_FRENZY_CONFIG.GRID_SIZE + col;
        const id = `block_${row}_${col}`;
        grid.push({
          id,
          row,
          col,
          isActive: false,
          index
        });
      }
    }

    return grid;
  }

  /**
   * Activate initial blocks when game starts
   */
  activateInitialBlocks(roomId: string, userId?: string): void {
    const key = `${roomId}:${userId ?? ''}`;
    const gameData = this.gameData.get(key);
    if (!gameData) {
      return;
    }

    // Activate 3 specific initial positions (matching client logic)
    const initialPositions = [
      { row: 1, col: 2 },
      { row: 2, col: 3 },
      { row: 0, col: 1 }
    ];

    gameData.activeBlockCount = 0;

    for (const pos of initialPositions) {
      const index = pos.row * FINGER_FRENZY_CONFIG.GRID_SIZE + pos.col;
      if (index < gameData.grid.length) {
        gameData.grid[index].isActive = true;
        gameData.activeBlockCount++;
      }
    }

    logger.info(`Activated ${gameData.activeBlockCount} initial blocks for room ${roomId}`);
  }

  /**
   * Handle a tile tap by ID
   */
  handleTileTap(roomId: string, tileId: string, reactionTime: number, userId?: string): TileTapResult {
    const gameState = this.gameService.getGameState(roomId, userId);
    const key = `${roomId}:${userId ?? ''}`;
    const gameData = this.gameData.get(key);

    if (!gameState || !gameData) {
      return {
        newBlock: null,
        success: false,
        isCorrect: false,
        points: 0,
        newScore: 0,
        newLives: 0,
        gameEnded: false,
        message: 'Game state not found'
      };
    }

    if (gameState.status !== 'active') {
      return {
        newBlock: null,
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        message: 'Game is not active'
      };
    }

    // Find the block by ID
    const block = gameData.grid.find(b => b.id === tileId);
    if (!block) {
      return {
        newBlock: null,
        success: false,
        isCorrect: false,
        points: 0,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
        message: 'Invalid tile ID'
      };
    }

    // Check if the tile is active (correct tap) or inactive (wrong tap)
    if (block.isActive) {
      // CORRECT TAP
      // Validate reaction time (should be reasonable)
      if (reactionTime < 0) {
        return {
          newBlock: null,
          success: false,
          isCorrect: false,
          points: 0,
          newScore: gameState.score,
          newLives: gameState.lives,
          gameEnded: false,
          message: 'Invalid reaction time'
        };
      }

      // Calculate points based on reaction time
      const points = this.calculatePoints(reactionTime);

      // Update score using GameService
      this.gameService.updateScore(roomId, points, "add", userId);

      // Deactivate the clicked block
      block.isActive = false;
      gameData.activeBlockCount--;

      // Activate a new random block (excluding the clicked one)
      const newBlock = this.activateRandomBlock(roomId, block.index, userId);

      logger.info(`Correct tap in room ${roomId}: tile ${tileId}, reaction time ${reactionTime}ms, points ${points}, new score ${gameState.score}`);

      return {
        newBlock,
        success: true,
        isCorrect: true,
        points,
        newScore: gameState.score,
        newLives: gameState.lives,
        gameEnded: false,
      };
    } else {
      // WRONG TAP
      // Apply penalty
      const penalty = FINGER_FRENZY_CONFIG.WRONG_CLICK_PENALTY;
      this.gameService.updateScore(roomId, penalty, "subtract", userId);

      // Deduct life using GameService
      const livesResult = this.gameService.deductLife(roomId, userId);

      // Check if game should end due to no lives
      if (livesResult.gameEnded) {
        this.endGame(roomId, 'no_lives', userId);
      }

      logger.info(`Wrong tap in room ${roomId}: tile ${tileId}, penalty ${penalty}, new score ${gameState.score}, lives ${livesResult.newLives}, game ended: ${livesResult.gameEnded}`);

      return {
        newBlock: null,
        success: true,
        isCorrect: false,
        points: penalty,
        newScore: gameState.score,
        newLives: livesResult.newLives,
        gameEnded: livesResult.gameEnded
      };
    }
  }

  /**
   * Get all tile states for the room
   */
  getAllTileStates(roomId: string, userId?: string): TileStates | null {
    const key = `${roomId}:${userId ?? ''}`;
    const gameData = this.gameData.get(key);
    if (!gameData) {
      return null;
    }

    return gameData.grid.reduce((states, block) => {
      states[block.id] = { isActive: block.isActive };
      return states;
    }, {} as TileStates);
  }

  /**
   * Calculate points based on reaction time
   */
  calculatePoints(reactionTime: number): number {
    if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.FAST) {
      return FINGER_FRENZY_CONFIG.SCORE_TIERS.FAST;
    }
    if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM_FAST) {
      return FINGER_FRENZY_CONFIG.SCORE_TIERS.MEDIUM_FAST;
    }
    if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM) {
      return FINGER_FRENZY_CONFIG.SCORE_TIERS.MEDIUM;
    }
    if (reactionTime < FINGER_FRENZY_CONFIG.SCORE_TIER_THRESHOLDS.MEDIUM_SLOW) {
      return FINGER_FRENZY_CONFIG.SCORE_TIERS.MEDIUM_SLOW;
    }
    return FINGER_FRENZY_CONFIG.SCORE_TIERS.SLOW;
  }

  /**
   * Activate a random block (excluding specified block and already active blocks)
   */
  activateRandomBlock(roomId: string, excludeBlockIndex?: number, userId?: string): GridBlock | null {
    const gameState = this.gameService.getGameState(roomId, userId);
    const key = `${roomId}:${userId ?? ''}`;
    const gameData = this.gameData.get(key);

    if (!gameState || !gameData || gameState.status !== 'active') {
      return null;
    }

    // Don't activate more blocks if we already have the maximum
    if (gameData.activeBlockCount >= FINGER_FRENZY_CONFIG.INITIAL_ACTIVE_BLOCKS) {
      return null;
    }

    // Get list of available positions (exclude currently active blocks and the excluded block)
    const availableIndices = gameData.grid
      .map((block, index) => ({ block, index }))
      .filter(({ block, index }) =>
        !block.isActive &&
        (excludeBlockIndex === undefined || index !== excludeBlockIndex)
      )
      .map(({ index }) => index);

    // If we have available positions, activate one deterministically via PRNG
    if (availableIndices.length > 0) {
      const idx = sessionService.getPrngInt(roomId, availableIndices.length);
      const randomIndex = availableIndices[idx];
      const block = gameData.grid[randomIndex];

      block.isActive = true;
      gameData.activeBlockCount++;

      logger.info(`Activated random block ${randomIndex} in room ${roomId}, active count: ${gameData.activeBlockCount}`);
      return block;
    }

    return null;
  }

  /**
   * Get grid state for client synchronization
   */
  getGridState(roomId: string, userId?: string): GridState | null {
    const key = `${roomId}:${userId ?? ''}`;
    const gameData = this.gameData.get(key);
    if (!gameData) {
      return null;
    }

    return {
      blocks: gameData.grid.map((block: GridBlock) => ({ ...block })), // Return a copy
      activeCount: gameData.activeBlockCount
    };
  }

  /**
   * Deactivate all blocks (useful for game end or reset)
   */
  deactivateAllBlocks(roomId: string, userId?: string): boolean {
    const key = `${roomId}:${userId ?? ''}`;
    const gameData = this.gameData.get(key);
    if (!gameData) {
      return false;
    }

    gameData.grid.forEach(block => block.isActive = false);
    gameData.activeBlockCount = 0;

    logger.info(`Deactivated all blocks in room ${roomId}`);
    return true;
  }

  /**
   * Submit score to external API
   */
  private async submitScore(roomId: string, gameState: any, reason: EndReason, submitScoreId?: string, authToken?: string): Promise<void> {
    try {
      const scoreData: ScoreSubmissionData = {
        roomId,
        score: gameState.score,
        scoreArray: gameState.scoreAry || [],
        submitScoreId,
        authToken
      };

      const result = await submitGameScore(scoreData);

      if (result.success) {
        logger.info(`Score submitted successfully for Finger Frenzy game in room ${roomId}`);
      } else {
        logger.warn(`Score submission failed for Finger Frenzy game in room ${roomId}: ${result.error}`);
      }
    } catch (error) {
      logger.error(`Error submitting score for Finger Frenzy game in room ${roomId}:`, error);
    }
  }

  /**
   * Clean up game state when room is destroyed
   */
  cleanupGame(roomId: string, userId?: string): boolean {
    const deleted = this.gameService.deleteGameState(roomId, userId);
    const key = `${roomId}:${userId ?? ''}`;
    this.gameData.delete(key);
    this.socketMap.delete(key);
    this.sessionData.delete(key);

    if (deleted) {
      logger.info(`Cleaned up Finger Frenzy game for room ${roomId}`);
    }
    return deleted;
  }

  /**
   * Public cleanup method for external cleanup (e.g., on socket disconnect)
   */
  public cleanup(roomId: string, userId?: string): void {
    const gameState = this.gameService.getGameState(roomId, userId);
    if (gameState && gameState.status === 'active') {
      // End the game due to disconnection
      this.endGame(roomId, 'manual', userId);
    } else {
      // Just clean up data if game wasn't active
      this.cleanupGame(roomId, userId);
    }
  }

  /**
   * Setup socket event handlers for Finger Frenzy
   */
  public setupSocketHandlers(socket: Socket): void {
    // Get authenticated user to check if this is a Finger Frenzy game
    const user = getAuthenticatedUser(socket);
    if (!user || user.gameId !== GAME_TYPES.FINGER_FRENZY) {
      return; // Only handle events for Finger Frenzy games
    }

    // Game initialization event (called at client load)
    socket.on('init', (data) => {
      this.handleGameInit(socket, data);
    });

    // Game start event (called after countdown)
    socket.on('start', (data) => {
      this.handleGameStart(socket, data);
    });

    // Generic game end event
    socket.on('end', (data) => {
      this.handleGameEnd(socket, data);
    });

    // Generic game action event (for game-specific actions)
    socket.on('action', (data) => {
      this.handleGameAction(socket, data);
    });
  }

  /**
   * Handle generic game action event
   */
  handleGameAction(socket: Socket, data: GameActionData): void {
    // Get authenticated user from socket (JWT contains room ID, game ID)
    const user = getAuthenticatedUser(socket);
    if (!user) {
      socket.emit('error', {
        message: 'Authentication required for game action'
      });
      return;
    }

    const { roomId, gameId, userId } = user;
    const { action } = data;

    if (!action) {
      socket.emit('error', {
        message: 'Missing action data'
      });
      return;
    }

    // Update user activity
    sessionService.updateUserActivity(userId);

    try {
      // Process the game action using GameService
      const gameAction: GameAction = {
        type: action.type,
        data: action.data,
        timestamp: Date.now()
      };

      const success = this.gameService.processGameAction(roomId, gameAction, userId);

      if (success) {
        // Handle specific action types for Finger Frenzy
        switch (action.type) {
          case 'tile_tap':
            this.handleTileTapAction(socket, data as TileTapActionData);
            break;
          default:
            socket.emit('error', {
              message: `Unknown action type: ${action.type}`
            });
        }
      } else {
        socket.emit('error', {
          message: 'Failed to process game action'
        });
      }
    } catch (error) {
      logger.error(`Error processing game action in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error during game action', 'action');
    }
  }

  /**
   * Emit a fatal error that stops the game instance and shows modal
   */
  private emitFatalError(socket: Socket, roomId: string, message: string, errorType: string): void {
    // End the game if it's active
    const gameState = this.gameService.getGameState(roomId);
    if (gameState && gameState.status === 'active') {
      this.endGame(roomId, 'manual');
    }

    // Emit fatal error event
    socket.emit('game_fatal_error', {
      message,
      errorType,
      roomId,
      timestamp: Date.now()
    });

    logger.error(`Fatal error in room ${roomId} (${errorType}): ${message}`);
  }

  /**
   * Handle game initialization event (called at client load)
   */
  handleGameInit(socket: Socket, data: any): void {
    // Get authenticated user from socket (JWT contains room ID, game ID, score submit ID)
    const user = getAuthenticatedUser(socket);
    if (!user) {
      this.emitFatalError(socket, '', 'Authentication required', 'initialization');
      return;
    }

    // Extract room ID, game ID, and score submit ID from JWT token
    const { roomId, gameId, scoreSubmitId, userId } = user;

    if (!roomId || !gameId || !scoreSubmitId) {
      this.emitFatalError(socket, roomId, 'Invalid JWT token - missing required game data', 'initialization');
      return;
    }

    // Update user activity
    sessionService.updateUserActivity(userId);

    logger.info(`FingerFrenzy game session ${roomId} - User: ${userId}, GameId: ${gameId}, SubmitScoreId: ${scoreSubmitId}`);

    try {
      // Initialize the game (but don't start it yet)
      const result = this.initializeGame(roomId, socket, userId);

      if (result.success && result.gameState) {
        // Get initial grid state (all blocks inactive)
        const gridState = this.getGridState(roomId, userId);

        socket.emit('initialized', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          gridState,
          message: 'Game initialized!'
        });

        logger.info(`${gameId} game initialized in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to initialize game', 'initialization');
      }
    } catch (error) {
      logger.error(`Error initializing ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error during initialization', 'initialization');
    }
  }

  /**
   * Handle game start event (called after countdown)
   */
  handleGameStart(socket: Socket, data: any): void {
    // Get authenticated user from socket (JWT contains room ID, game ID)
    const user = getAuthenticatedUser(socket);
    if (!user) {
      this.emitFatalError(socket, '', 'Authentication required', 'game_start');
      return;
    }

    const { roomId, gameId, userId } = user;

    if (!roomId || !gameId) {
      this.emitFatalError(socket, roomId, 'Missing roomId, or gameId', 'start');
      return;
    }

    try {
      // Start the game (game should already be initialized)
      const result = this.startGame(roomId, socket, userId);

      if (result.success && result.gameState) {
        // Get initial grid state with active blocks
        const gridState = this.getGridState(roomId, userId);

        socket.emit('started', {
          gameState: {
            score: result.gameState.score,
            lives: result.gameState.lives,
            isActive: result.gameState.status === 'active',
            startTime: result.gameState.startTime
          },
          gridState,
          message: 'Game started!'
        });

        logger.info(`${gameId} game started in room ${roomId}`);
      } else {
        this.emitFatalError(socket, roomId, result.message || 'Failed to start game', 'start');
      }
    } catch (error) {
      logger.error(`Error starting ${gameId} game in room ${roomId}:`, error);
      this.emitFatalError(socket, roomId, 'Internal server error during game start', 'start');
    }
  }

  /**
   * Handle generic game end event
   */
   async handleGameEnd(socket: Socket, data: any): Promise<void> {
    // Get authenticated user from socket (JWT contains room ID, game ID)
    const user = getAuthenticatedUser(socket);
    if (!user) {
      socket.emit('error', {
        message: 'Authentication required for game end'
      });
      return;
    }

    const { roomId, gameId, userId } = user;
    const { reason = 'manual' } = data;

    // Update user activity
    sessionService.updateUserActivity(userId);

    try {
      const gameState = this.gameService.getGameState(roomId, userId);
      if (!gameState) {
        socket.emit('error', {
          message: 'Game state not found'
        });
        return;
      }

      this.endGame(roomId, reason, userId);
      // Note: endGame() method already emitted the 'ended' event

      logger.info(`${gameId} game ended in room ${roomId}, reason: ${reason}`);
    } catch (error) {
      logger.error(`Error ending ${gameId} game in room ${roomId}:`, error);
      socket.emit('error', {
        message: 'Internal server error'
      });
    }
  }

  /**
   * Handle tile tap action
   */
  handleTileTapAction(socket: Socket, data: TileTapActionData): void {
    // Get authenticated user from socket (JWT contains room ID, game ID)
    const user = getAuthenticatedUser(socket);
    if (!user) {
      socket.emit('error', {
        message: 'Authentication required for tile tap action'
      });
      return;
    }

    const { roomId, gameId, userId } = user;
    const { action } = data;
    const { tileId, reactionTime, clickTime } = action.data;

    if (!tileId) {
      socket.emit('error', {
        message: 'Missing tileId for tile tap action'
      });
      return;
    }

    // Update user activity
    sessionService.updateUserActivity(userId);

    try {
      // Validate the tap timing
      const currentTime = Date.now();
      const timeDiff = Math.abs(currentTime - (clickTime || currentTime));
      if (timeDiff > 5000) { // 5 seconds tolerance
        socket.emit('error', {
          message: 'Tap time too far from current time'
        });
        return;
      }

      // Process the tile tap
      const result = this.handleTileTap(roomId, tileId, reactionTime || 0, userId);

      if (result.success) {
        // Get updated grid state and tile states
        const gridState = this.getGridState(roomId, userId);
        const tileStates = this.getAllTileStates(roomId, userId);

        // Broadcast the action result to all players in room
        socket.emit('action_result', {
          actionType: 'tile_tap',
          data: {
            tileId,
            isCorrect: result.isCorrect,
            points: result.points,
            newScore: result.newScore,
            newLives: result.newLives,
            gameEnded: result.gameEnded,
            gridState,
            tileStates,
          }
        });

        // If game ended, broadcast game end event
        if (result.gameEnded) {
          socket.emit('ended', {
            reason: 'no_lives',
            finalScore: result.newScore
          });
        }

        logger.info(`Tile tap processed for room ${roomId}, tile ${tileId}, correct: ${result.isCorrect}, points: ${result.points}`);
      } else {
        socket.emit('game_error', {
          message: result.message || 'Failed to process tile tap'
        });
      }
    } catch (error) {
      logger.error(`Error processing tile tap in room ${roomId}:`, error);
      socket.emit('game_error', {
        message: 'Internal server error'
      });
    }
  }
}