import { GameType } from './game';

/**
 * Room status enumeration
 */
export enum RoomStatus {
  Waiting = 'waiting',
  Starting = 'starting',
  Active = 'active',
  Ended = 'ended',
  Abandoned = 'abandoned'
}

/**
 * Player role enumeration
 */
export enum PlayerRole {
  Host = 'host',
  Player = 'player',
  Spectator = 'spectator'
}

/**
 * Room player interface
 */
export interface RoomPlayer {
  userId: string;
  name: string;
  displayName: string;
  role: PlayerRole;
  isReady: boolean;
  isConnected: boolean;
  joinedAt: Date;
  lastActivity: Date;
}

/**
 * Room configuration interface
 */
export interface RoomConfig {
  gameType: GameType;
  maxPlayers: number;
  isPrivate: boolean;
  allowSpectators: boolean;
  hostOnlyStart: boolean;
  autoStart: boolean;
  autoStartDelay: number;
  timeLimit: number;
  roomName?: string;
  description?: string;
}

/**
 * Room interface
 */
export interface Room {
  id: string;
  code: string;
  name?: string;
  description?: string;
  config: RoomConfig;
  status: RoomStatus;
  players: RoomPlayer[];
  spectators: RoomPlayer[];
  hostUserId: string;
  currentPlayers: number;
  maxPlayers: number;
  gameType: GameType;
  isPrivate: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastActivity: Date;
}

/**
 * Create room request interface
 */
export interface CreateRoomRequest {
  gameType: GameType;
  hostUserId: string;
  hostName: string;
  config?: Partial<RoomConfig>;
}

/**
 * Join room request interface
 */
export interface JoinRoomRequest {
  roomId?: string;
  roomCode?: string;
  userId: string;
  userName: string;
  role?: PlayerRole;
}

/**
 * Room invitation interface
 */
export interface RoomInvitation {
  id: string;
  roomId: string;
  fromUserId: string;
  fromUserName: string;
  toUserId: string;
  toUserName: string;
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: Date;
  expiresAt: Date;
}

/**
 * Room chat message interface
 */
export interface RoomChatMessage {
  id: string;
  roomId: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: Date;
  type: 'user' | 'system' | 'game';
}

/**
 * Room event interface
 */
export interface RoomEvent {
  id: string;
  roomId: string;
  type: string;
  userId?: string;
  data?: Record<string, any>;
  timestamp: Date;
}

/**
 * Default room configuration
 */
export const DEFAULT_ROOM_CONFIG: RoomConfig = {
  gameType: 'default',
  maxPlayers: 2,
  isPrivate: false,
  allowSpectators: true,
  hostOnlyStart: true,
  autoStart: false,
  autoStartDelay: 10,
  timeLimit: 300 // 5 minutes default
};

/**
 * Room validation rules
 */
export const ROOM_VALIDATION = {
  MAX_PLAYERS: {
    min: 2,
    max: 16
  },
  TIME_LIMIT: {
    min: 30, // 30 seconds
    max: 1800 // 30 minutes
  },
  PASSWORD: {
    minLength: 4,
    maxLength: 20
  },
  ROOM_NAME: {
    minLength: 3,
    maxLength: 50
  },
  DESCRIPTION: {
    maxLength: 200
  },
  AUTO_START_DELAY: {
    min: 5,
    max: 60
  }
};

/**
 * Room capacity limits by game type
 */
export const GAME_TYPE_LIMITS:  { min: number; max: number; } = {min: 2, max: 2 };
