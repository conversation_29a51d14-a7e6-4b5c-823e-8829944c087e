

export const index = 0;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_layout.svelte.js')).default;
export const universal = {
  "ssr": false
};
export const universal_id = "src/routes/+layout.ts";
export const imports = ["_app/immutable/nodes/0.GHmX8_TY.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/CBPGQi6i.js"];
export const stylesheets = ["_app/immutable/assets/0.PIix4Fxn.css"];
export const fonts = [];
