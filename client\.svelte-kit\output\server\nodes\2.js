

export const index = 2;
let component_cache;
export const component = async () => component_cache ??= (await import('../entries/pages/_page.svelte.js')).default;
export const imports = ["_app/immutable/nodes/2.CBmOZecg.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/Dh9ZwQ9u.js","_app/immutable/chunks/CBPGQi6i.js","_app/immutable/chunks/D7HYObST.js","_app/immutable/chunks/6c5zsAwx.js","_app/immutable/chunks/CagF9jJf.js","_app/immutable/chunks/CbN9a2Ga.js"];
export const stylesheets = ["_app/immutable/assets/MumsNumbers.Cfre7piE.css","_app/immutable/assets/2.BN6ceEAQ.css"];
export const fonts = [];
