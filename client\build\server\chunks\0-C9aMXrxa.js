const index = 0;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-CIzT1gSW.js')).default;
const universal = {
  "ssr": false
};
const universal_id = "src/routes/+layout.ts";
const imports = ["_app/immutable/nodes/0.GHmX8_TY.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/CBPGQi6i.js"];
const stylesheets = ["_app/immutable/assets/0.PIix4Fxn.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, universal, universal_id };
//# sourceMappingURL=0-C9aMXrxa.js.map
