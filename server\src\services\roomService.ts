import { 
  Room, 
  RoomPlayer, 
  RoomConfig, 
  CreateRoomRequest, 
  JoinRoomRequest, 
  RoomInvitation, 
  RoomChatMessage, 
  RoomEvent,
  RoomStatus,
  PlayerRole,
  DEFAULT_ROOM_CONFIG,
  ROOM_VALIDATION,
  GAME_TYPE_LIMITS
} from '../types/room';
import { GameType } from '../types/game';
import { logger } from '../utils/logger';

/**
 * Enhanced Room Service for comprehensive room management
 */
export class RoomService {
  private rooms: Map<string, Room> = new Map();
  private roomsByCode: Map<string, string> = new Map(); // code -> roomId
  private invitations: Map<string, RoomInvitation> = new Map();
  private chatMessages: Map<string, RoomChatMessage[]> = new Map(); // roomId -> messages
  private roomEvents: Map<string, RoomEvent[]> = new Map(); // roomId -> events
  private bannedUsers: Map<string, Set<string>> = new Map(); // roomId -> Set<userId>

  /**
   * Create a new room
   */
  async createRoom(request: CreateRoomRequest): Promise<Room> {
    const roomId = this.generateRoomId();
    const roomCode = this.generateRoomCode();
    
    // Validate and merge configuration
    const config = this.validateRoomConfig({ ...DEFAULT_ROOM_CONFIG, ...request.config });
    
    // Create host player
    const hostPlayer: RoomPlayer = {
      userId: request.hostUserId,
      name: request.hostName,
      displayName: request.hostName,
      role: PlayerRole.Host,
      isReady: true,
      isConnected: true,
      joinedAt: new Date(),
      lastActivity: new Date()
    };

    const room: Room = {
      id: roomId,
      code: roomCode,
      name: config.roomName,
      description: config.description,
      config,
      status: RoomStatus.Waiting,
      players: [hostPlayer],
      spectators: [],
      hostUserId: request.hostUserId,
      currentPlayers: 1,
      maxPlayers: config.maxPlayers,
      gameType: config.gameType,
      isPrivate: config.isPrivate,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastActivity: new Date()
    };



    this.rooms.set(roomId, room);
    this.roomsByCode.set(roomCode, roomId);
    this.chatMessages.set(roomId, []);
    this.roomEvents.set(roomId, []);

    this.logRoomEvent(roomId, 'created', request.hostUserId, { config });

    logger.info('Room created', {
      roomId,
      roomCode,
      hostUserId: request.hostUserId,
      gameType: config.gameType,
      isPrivate: config.isPrivate
    });

    return room;
  }

  /**
   * Join a room
   */
  async joinRoom(request: JoinRoomRequest): Promise<{ room: Room; player: RoomPlayer }> {
    const room = this.getRoomByIdOrCode(request.roomId, request.roomCode);
    if (!room) {
      throw new Error('Room not found');
    }

    // Validate join request
    await this.validateJoinRequest(room, request);

    // Check if user is already in room
    const existingPlayer = room.players.find(p => p.userId === request.userId);
    if (existingPlayer) {
      // Update connection status and return
      existingPlayer.isConnected = true;
      existingPlayer.lastActivity = new Date();
      room.updatedAt = new Date();
      room.lastActivity = new Date();
      return { room, player: existingPlayer };
    }

    // Create new player
    const player: RoomPlayer = {
      userId: request.userId,
      name: request.userName,
      displayName: request.userName,
      role: request.role || PlayerRole.Player,
      isReady: true,
      isConnected: true,
      joinedAt: new Date(),
      lastActivity: new Date()
    };

    // Add player to appropriate list
    if (player.role === PlayerRole.Spectator) {
      room.spectators.push(player);
    } else {
      room.players.push(player);
      room.currentPlayers++;
    }

    room.updatedAt = new Date();
    room.lastActivity = new Date();

    this.logRoomEvent(room.id, 'joined', request.userId, { role: player.role });

    logger.info('Player joined room', {
      roomId: room.id,
      userId: request.userId,
      role: player.role,
      currentPlayers: room.currentPlayers
    });

    return { room, player };
  }

  /**
   * Leave a room
   */
  leaveRoom(roomId: string, userId: string): { room: Room | null; wasHost: boolean } {
    const room = this.rooms.get(roomId);
    if (!room) {
      return { room: null, wasHost: false };
    }

    const wasHost = room.hostUserId === userId;
    
    // Remove player from players or spectators
    room.players = room.players.filter(p => p.userId !== userId);
    room.spectators = room.spectators.filter(p => p.userId !== userId);
    
    room.currentPlayers = room.players.length;
    room.updatedAt = new Date();
    room.lastActivity = new Date();

    // Handle host transfer if needed
    if (wasHost && room.players.length > 0) {
      const newHost = room.players[0];
      room.hostUserId = newHost.userId;
      newHost.role = PlayerRole.Host;
      
      this.logRoomEvent(roomId, 'host_changed', newHost.userId, { 
        oldHost: userId, 
        newHost: newHost.userId 
      });
    }

    // Clean up empty room
    if (room.players.length === 0) {
      this.deleteRoom(roomId);
      return { room: null, wasHost };
    }

    this.logRoomEvent(roomId, 'left', userId, { wasHost });

    logger.info('Player left room', {
      roomId,
      userId,
      wasHost,
      remainingPlayers: room.currentPlayers
    });

    return { room, wasHost };
  }

  /**
   * Get room by ID or code
   */
  getRoomByIdOrCode(roomId?: string, roomCode?: string): Room | null {
    if (roomId) {
      return this.rooms.get(roomId) || null;
    }
    if (roomCode) {
      const id = this.roomsByCode.get(roomCode);
      return id ? this.rooms.get(id) || null : null;
    }
    return null;
  }

  /**
   * Get room by ID
   */
  getRoom(roomId: string): Room | null {
    return this.rooms.get(roomId) || null;
  }

  /**
   * Update player ready status
   */
  updatePlayerReady(roomId: string, userId: string, isReady: boolean): Room | null {
    const room = this.rooms.get(roomId);
    if (!room) return null;

    const player = room.players.find(p => p.userId === userId) || 
                   room.spectators.find(p => p.userId === userId);
    
    if (!player) return null;

    player.isReady = isReady;
    room.updatedAt = new Date();
    room.lastActivity = new Date();

    this.logRoomEvent(roomId, 'player_ready', userId, { isReady });

    return room;
  }

  /**
   * Check if all players are ready
   */
  areAllPlayersReady(roomId: string): boolean {
    const room = this.rooms.get(roomId);
    if (!room || room.players.length === 0) return false;

    return room.players.every(player => player.isReady);
  }

  /**
   * Update room status
   */
  updateRoomStatus(roomId: string, status: RoomStatus): Room | null {
    const room = this.rooms.get(roomId);
    if (!room) return null;

    room.status = status;
    room.updatedAt = new Date();
    room.lastActivity = new Date();

    this.logRoomEvent(roomId, 'status_changed', undefined, { status });

    return room;
  }

  /**
   * Get all rooms (for admin/debugging)
   */
  getAllRooms(): Room[] {
    return Array.from(this.rooms.values());
  }

  /**
   * Get rooms by game type
   */
  getRoomsByGameType(gameType: GameType): Room[] {
    return Array.from(this.rooms.values()).filter(room => room.gameType === gameType);
  }

  /**
   * Get public rooms (non-private, waiting status)
   */
  getPublicRooms(gameType?: GameType): Room[] {
    return Array.from(this.rooms.values()).filter(room => 
      !room.isPrivate && 
      room.status === RoomStatus.Waiting &&
      (!gameType || room.gameType === gameType)
    );
  }

  /**
   * Private helper methods
   */
  private generateRoomId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  private generateRoomCode(): string {
    // Generate a 6-character alphanumeric code
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  private validateRoomConfig(config: Partial<RoomConfig>): RoomConfig {
    const validatedConfig = { ...DEFAULT_ROOM_CONFIG, ...config };

    // Validate max players
    if (validatedConfig.maxPlayers < ROOM_VALIDATION.MAX_PLAYERS.min ||
        validatedConfig.maxPlayers > ROOM_VALIDATION.MAX_PLAYERS.max) {
      throw new Error(`Max players must be between ${ROOM_VALIDATION.MAX_PLAYERS.min} and ${ROOM_VALIDATION.MAX_PLAYERS.max}`);
    }

    // Validate game type limits
    const gameTypeLimits = GAME_TYPE_LIMITS;
    if (gameTypeLimits && validatedConfig.maxPlayers > gameTypeLimits.max) {
      validatedConfig.maxPlayers = gameTypeLimits.max;
    }

    // Validate time limit
    if (validatedConfig.timeLimit < ROOM_VALIDATION.TIME_LIMIT.min ||
        validatedConfig.timeLimit > ROOM_VALIDATION.TIME_LIMIT.max) {
      throw new Error(`Time limit must be between ${ROOM_VALIDATION.TIME_LIMIT.min} and ${ROOM_VALIDATION.TIME_LIMIT.max} seconds`);
    }

    return validatedConfig;
  }

  /**
   * Validate join request
   */
  private async validateJoinRequest(room: Room, request: JoinRoomRequest): Promise<void> {
    // Check if room is full
    if (request.role !== PlayerRole.Spectator && room.currentPlayers >= room.maxPlayers) {
      throw new Error('Room is full');
    }

    // Check if spectators are allowed
    if (request.role === PlayerRole.Spectator && !room.config.allowSpectators) {
      throw new Error('Spectators are not allowed in this room');
    }



    // Check if user is banned
    const bannedUsers = this.bannedUsers.get(room.id);
    if (bannedUsers && bannedUsers.has(request.userId)) {
      throw new Error('You are banned from this room');
    }

    // Check room status
    if (room.status === RoomStatus.Active || room.status === RoomStatus.Ended) {
      if (request.role !== PlayerRole.Spectator) {
        throw new Error('Cannot join as player - game is already in progress');
      }
    }
  }

  /**
   * Delete a room
   */
  private deleteRoom(roomId: string): void {
    const room = this.rooms.get(roomId);
    if (room) {
      this.roomsByCode.delete(room.code);
      this.rooms.delete(roomId);
      this.chatMessages.delete(roomId);
      this.roomEvents.delete(roomId);
      this.bannedUsers.delete(roomId);

      logger.info('Room deleted', { roomId });
    }
  }

  /**
   * Log room event
   */
  private logRoomEvent(roomId: string, type: string, userId?: string, data?: Record<string, any>): void {
    const events = this.roomEvents.get(roomId) || [];
    const event: RoomEvent = {
      id: Math.random().toString(36).substring(2, 15),
      roomId,
      type,
      userId,
      data,
      timestamp: new Date()
    };

    events.push(event);
    this.roomEvents.set(roomId, events);

    // Keep only last 100 events per room
    if (events.length > 100) {
      events.splice(0, events.length - 100);
    }
  }

  /**
   * Clean up inactive rooms
   */
  cleanupInactiveRooms(maxIdleTime: number = 30 * 60 * 1000): void {
    const now = new Date();
    const roomsToDelete: string[] = [];

    for (const [roomId, room] of this.rooms.entries()) {
      if (now.getTime() - room.lastActivity.getTime() > maxIdleTime) {
        roomsToDelete.push(roomId);
      }
    }

    for (const roomId of roomsToDelete) {
      logger.info('Cleaning up inactive room', { roomId });
      this.deleteRoom(roomId);
    }
  }
}

// Singleton instance
export const roomService = new RoomService();
