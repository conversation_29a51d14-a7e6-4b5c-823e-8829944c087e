{"version": 3, "file": "state.svelte-DJsENWvz.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/state.svelte.js"], "sourcesContent": ["import { V as noop } from \"./index.js\";\nconst is_legacy = noop.toString().includes(\"$$\") || /function \\w+\\(\\) \\{\\}/.test(noop.toString());\nif (is_legacy) {\n  ({\n    data: {},\n    form: null,\n    error: null,\n    params: {},\n    route: { id: null },\n    state: {},\n    status: -1,\n    url: new URL(\"https://example.com\")\n  });\n}\n"], "names": [], "mappings": ";;AACA,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjG,IAAI,SAAS,EAAE;AACf,EAAE,CAAC;AACH,IAOI,GAAG,EAAE,IAAI,GAAG,CAAC,qBAAqB;AACtC,GAAG;AACH"}