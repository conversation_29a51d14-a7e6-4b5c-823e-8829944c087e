<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <!-- Target frame for displaying target faces -->
  <defs>
    <linearGradient id="frameGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF8C00;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Outer frame -->
  <rect x="5" y="5" width="90" height="90" rx="10" ry="10" 
        fill="url(#frameGradient)" 
        stroke="#B8860B" 
        stroke-width="2" 
        filter="url(#shadow)"/>
  
  <!-- Inner frame -->
  <rect x="10" y="10" width="80" height="80" rx="8" ry="8" 
        fill="none" 
        stroke="#FFFFFF" 
        stroke-width="1" 
        opacity="0.8"/>
  
  <!-- Corner decorations -->
  <circle cx="15" cy="15" r="3" fill="#FFFFFF" opacity="0.6"/>
  <circle cx="85" cy="15" r="3" fill="#FFFFFF" opacity="0.6"/>
  <circle cx="15" cy="85" r="3" fill="#FFFFFF" opacity="0.6"/>
  <circle cx="85" cy="85" r="3" fill="#FFFFFF" opacity="0.6"/>
</svg>
