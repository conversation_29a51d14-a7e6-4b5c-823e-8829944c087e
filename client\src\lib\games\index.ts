import { FingerFrenzyGame } from './FingerFrenzy';
import { BingoGame } from './Bingo';
import { MatchingMayhemGame } from './MatchingMayhem';
import { NumberSequenceGame } from './NumberSequence';
import { MumsNumbersGame } from './NumberConnect';
import { FindingLuigiGame } from './FindingLuigi';
import type { SocketClient } from '$lib/socket';

// Game factory function
export function createGame(gameId: string, containerId: string, socketClient: SocketClient, callbacks?: any) {
	const config = {
		gameId,
		containerId,
		socketClient,
		...callbacks
	};

	switch (gameId) {
		case 'finger-frenzy':
			return new FingerFrenzyGame(config);
		case 'bingo':
			return new BingoGame(config);
		case 'matching-mayhem':
			return new MatchingMayhemGame(config);
		case 'numbers':
			return new NumberSequenceGame(config);
		case 'mums-numbers':
			return new MumsNumbersGame(config);
		case 'finding-luigi':
			return new FindingLuigiGame(config);
		default:
			throw new Error(`Unknown game ID: ${gameId}`);
	}
}
