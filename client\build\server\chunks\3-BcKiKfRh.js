const index = 3;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-rP_g-4vH.js')).default;
const universal = {
  "ssr": false
};
const universal_id = "src/routes/game/[id]/+page.ts";
const imports = ["_app/immutable/nodes/3.DQDtIKgH.js","_app/immutable/chunks/sPbygbFs.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/CBPGQi6i.js","_app/immutable/chunks/D7HYObST.js","_app/immutable/chunks/6c5zsAwx.js","_app/immutable/chunks/XXcWJRIr.js","_app/immutable/chunks/CbN9a2Ga.js","_app/immutable/chunks/CagF9jJf.js","_app/immutable/chunks/Dh9ZwQ9u.js"];
const stylesheets = ["_app/immutable/assets/MumsNumbers.Cfre7piE.css"];
const fonts = [];

export { component, fonts, imports, index, stylesheets, universal, universal_id };
//# sourceMappingURL=3-BcKiKfRh.js.map
