import"../chunks/DsnmJJEf.js";import{i as z}from"../chunks/Dh9ZwQ9u.js";import{p as F,f as u,b as m,c as G,$ as N,s as p,d as s,r as a,g as l,n as y,t as _}from"../chunks/CBPGQi6i.js";import{h as C,s as v,e as J}from"../chunks/D7HYObST.js";import{i as L}from"../chunks/6c5zsAwx.js";import{e as j,i as E,s as I,g as W}from"../chunks/CagF9jJf.js";import{g as A}from"../chunks/CbN9a2Ga.js";var B=u('<meta name="description" content="Select from our collection of exciting mini-games"/>'),P=u('<button><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-white/20 rounded-full text-sm font-medium text-white">Available</div></div> <h2 class="text-2xl font-bold text-white mb-2 group-hover:text-yellow-200 transition-colors"> </h2> <p class="text-white/80 text-sm leading-relaxed mb-4"> </p> <div class="flex items-center text-white/60 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg> Play Now</div></button>'),U=u('<div class="block h-full p-6 rounded-xl bg-gradient-to-br from-gray-600 to-gray-700 shadow-lg border border-white/10 opacity-60 cursor-not-allowed"><div class="flex items-center justify-between mb-4"><div class="px-3 py-1 bg-gray-500/50 rounded-full text-sm font-medium text-gray-300">Coming Soon</div></div> <h2 class="text-2xl font-bold text-gray-300 mb-2"> </h2> <p class="text-gray-400 text-sm leading-relaxed mb-4"> </p> <div class="flex items-center text-gray-500 text-sm"><svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg> Not Available</div></div>'),O=u('<div class="game-card group svelte-um5wpz"><!></div>'),q=u('<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"><header class="text-center py-12 px-4"><h1 class="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">TicTaps Games</h1></header>  <main class="container mx-auto px-4 pb-12"><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-4xl mx-auto"></div></main></div>');function Z(k,T){F(T,!1);const b=[{id:"finger-frenzy",name:"Finger Frenzy",description:"Fast-paced tapping game",color:"from-red-500 to-orange-500",available:!0},{id:"bingo",name:"Bingo",description:"Classic bingo game",color:"from-blue-500 to-purple-500",available:!0},{id:"matching-mayhem",name:"Matching Mayhem",description:"Memory matching game",color:"from-green-500 to-teal-500",available:!0},{id:"numbers",name:"Number Sequence",description:"Number pattern recognition",color:"from-purple-500 to-pink-500",available:!0},{id:"mums-numbers",name:"Mums Numbers!",description:"Draw a continuous path through numbers 1-5 covering all grid cells",color:"from-cyan-500 to-blue-500",available:!0}];async function $(t){try{const i=await fetch("http://localhost:3000/api/generate-token",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({gameId:t,clientSeed:"1234567890",username:`Player-${Math.floor(Math.random()*1e3)}`})});if(!i.ok)throw new Error(`Failed to generate token: ${i.statusText}`);const r=await i.json();if(!r.success||!r.token)throw new Error("Invalid response from token generation endpoint");return console.log("Generated JWT token data:",{gameId:r.userData.gameId,roomId:r.userData.roomId,userId:r.userData.userId,username:r.userData.username}),W.setRoomData(r.userData.roomId,r.token,r.submitScoreId),r.token}catch(e){throw console.error("Error generating JWT token:",e),e}}async function M(t){if(!b.find(e=>e.id===t)?.available){console.warn(`Game ${t} is not available`);return}try{const e=await $(t);console.log(`Generated JWT for ${t}:`,e),A(`/game/${t}?token=${e}`)}catch(e){console.error(`Failed to start game ${t}:`,e),alert(`Failed to start game: ${e instanceof Error?e.message:"Unknown error"}`)}}z();var g=q();C(t=>{var e=B();N.title="TicTaps Games",m(t,e)});var x=p(s(g),2),w=s(x);j(w,5,()=>b,E,(t,e)=>{var i=O(),r=s(i);{var D=d=>{var o=P(),n=p(s(o),2),f=s(n,!0);a(n);var c=p(n,2),h=s(c,!0);a(c),y(2),a(o),_(()=>{I(o,1,`block h-full w-full p-6 rounded-xl bg-gradient-to-br ${l(e).color??""} shadow-lg hover:shadow-2xl transform hover:scale-105 transition-all duration-300 border border-white/10 text-left cursor-pointer`,"svelte-um5wpz"),v(f,l(e).name),v(h,l(e).description)}),J("click",o,()=>M(l(e).id)),m(d,o)},S=d=>{var o=U(),n=p(s(o),2),f=s(n,!0);a(n);var c=p(n,2),h=s(c,!0);a(c),y(2),a(o),_(()=>{v(f,l(e).name),v(h,l(e).description)}),m(d,o)};L(r,d=>{l(e).available?d(D):d(S,!1)})}a(i),m(t,i)}),a(w),a(x),a(g),m(k,g),G()}export{Z as component};
