import{w as Ee,o as De,q as mt}from"./D7HYObST.js";import{aa as T,g as x,V as P}from"./CBPGQi6i.js";class le{constructor(t,n){this.status=t,typeof n=="string"?this.body={message:n}:n?this.body=n:this.body={message:`Error: ${t}`}}toString(){return JSON.stringify(this.body)}}class Se{constructor(t,n){this.status=t,this.location=n}}class Re extends Error{constructor(t,n,r){super(r),this.status=t,this.text=n}}new URL("sveltekit-internal://");function _t(e,t){return e==="/"||t==="ignore"?e:t==="never"?e.endsWith("/")?e.slice(0,-1):e:t==="always"&&!e.endsWith("/")?e+"/":e}function yt(e){return e.split("%25").map(decodeURI).join("%25")}function wt(e){for(const t in e)e[t]=decodeURIComponent(e[t]);return e}function me({href:e}){return e.split("#")[0]}function vt(e,t,n,r=!1){const a=new URL(e);Object.defineProperty(a,"searchParams",{value:new Proxy(a.searchParams,{get(i,o){if(o==="get"||o==="getAll"||o==="has")return f=>(n(f),i[o](f));t();const c=Reflect.get(i,o);return typeof c=="function"?c.bind(i):c}}),enumerable:!0,configurable:!0});const s=["href","pathname","search","toString","toJSON"];r&&s.push("hash");for(const i of s)Object.defineProperty(a,i,{get(){return t(),e[i]},enumerable:!0,configurable:!0});return a}function bt(...e){let t=5381;for(const n of e)if(typeof n=="string"){let r=n.length;for(;r;)t=t*33^n.charCodeAt(--r)}else if(ArrayBuffer.isView(n)){const r=new Uint8Array(n.buffer,n.byteOffset,n.byteLength);let a=r.length;for(;a;)t=t*33^r[--a]}else throw new TypeError("value must be a string or TypedArray");return(t>>>0).toString(36)}function At(e){const t=atob(e),n=new Uint8Array(t.length);for(let r=0;r<t.length;r++)n[r]=t.charCodeAt(r);return n.buffer}const kt=window.fetch;window.fetch=(e,t)=>((e instanceof Request?e.method:t?.method||"GET")!=="GET"&&G.delete(Ie(e)),kt(e,t));const G=new Map;function Et(e,t){const n=Ie(e,t),r=document.querySelector(n);if(r?.textContent){let{body:a,...s}=JSON.parse(r.textContent);const i=r.getAttribute("data-ttl");return i&&G.set(n,{body:a,init:s,ttl:1e3*Number(i)}),r.getAttribute("data-b64")!==null&&(a=At(a)),Promise.resolve(new Response(a,s))}return window.fetch(e,t)}function St(e,t,n){if(G.size>0){const r=Ie(e,n),a=G.get(r);if(a){if(performance.now()<a.ttl&&["default","force-cache","only-if-cached",void 0].includes(n?.cache))return new Response(a.body,a.init);G.delete(r)}}return window.fetch(t,n)}function Ie(e,t){let r=`script[data-sveltekit-fetched][data-url=${JSON.stringify(e instanceof Request?e.url:e)}]`;if(t?.headers||t?.body){const a=[];t.headers&&a.push([...new Headers(t.headers)].join(",")),t.body&&(typeof t.body=="string"||ArrayBuffer.isView(t.body))&&a.push(t.body),r+=`[data-hash="${bt(...a)}"]`}return r}const Rt=/^(\[)?(\.\.\.)?(\w+)(?:=(\w+))?(\])?$/;function It(e){const t=[];return{pattern:e==="/"?/^\/$/:new RegExp(`^${Lt(e).map(r=>{const a=/^\[\.\.\.(\w+)(?:=(\w+))?\]$/.exec(r);if(a)return t.push({name:a[1],matcher:a[2],optional:!1,rest:!0,chained:!0}),"(?:/(.*))?";const s=/^\[\[(\w+)(?:=(\w+))?\]\]$/.exec(r);if(s)return t.push({name:s[1],matcher:s[2],optional:!0,rest:!1,chained:!0}),"(?:/([^/]+))?";if(!r)return;const i=r.split(/\[(.+?)\](?!\])/);return"/"+i.map((c,f)=>{if(f%2){if(c.startsWith("x+"))return _e(String.fromCharCode(parseInt(c.slice(2),16)));if(c.startsWith("u+"))return _e(String.fromCharCode(...c.slice(2).split("-").map(_=>parseInt(_,16))));const d=Rt.exec(c),[,h,u,l,p]=d;return t.push({name:l,matcher:p,optional:!!h,rest:!!u,chained:u?f===1&&i[0]==="":!1}),u?"(.*?)":h?"([^/]*)?":"([^/]+?)"}return _e(c)}).join("")}).join("")}/?$`),params:t}}function Ut(e){return!/^\([^)]+\)$/.test(e)}function Lt(e){return e.slice(1).split("/").filter(Ut)}function Tt(e,t,n){const r={},a=e.slice(1),s=a.filter(o=>o!==void 0);let i=0;for(let o=0;o<t.length;o+=1){const c=t[o];let f=a[o-i];if(c.chained&&c.rest&&i&&(f=a.slice(o-i,o+1).filter(d=>d).join("/"),i=0),f===void 0){c.rest&&(r[c.name]="");continue}if(!c.matcher||n[c.matcher](f)){r[c.name]=f;const d=t[o+1],h=a[o+1];d&&!d.rest&&d.optional&&h&&c.chained&&(i=0),!d&&!h&&Object.keys(r).length===s.length&&(i=0);continue}if(c.optional&&c.chained){i++;continue}return}if(!i)return r}function _e(e){return e.normalize().replace(/[[\]]/g,"\\$&").replace(/%/g,"%25").replace(/\//g,"%2[Ff]").replace(/\?/g,"%3[Ff]").replace(/#/g,"%23").replace(/[.*+?^${}()|\\]/g,"\\$&")}function xt({nodes:e,server_loads:t,dictionary:n,matchers:r}){const a=new Set(t);return Object.entries(n).map(([o,[c,f,d]])=>{const{pattern:h,params:u}=It(o),l={id:o,exec:p=>{const _=h.exec(p);if(_)return Tt(_,u,r)},errors:[1,...d||[]].map(p=>e[p]),layouts:[0,...f||[]].map(i),leaf:s(c)};return l.errors.length=l.layouts.length=Math.max(l.errors.length,l.layouts.length),l});function s(o){const c=o<0;return c&&(o=~o),[c,e[o]]}function i(o){return o===void 0?o:[a.has(o),e[o]]}}function Ye(e,t=JSON.parse){try{return t(sessionStorage[e])}catch{}}function Ve(e,t,n=JSON.stringify){const r=n(t);try{sessionStorage[e]=r}catch{}}const U=globalThis.__sveltekit_1grspw7?.base??"",Pt=globalThis.__sveltekit_1grspw7?.assets??U,Ct="1759149738778",Je="sveltekit:snapshot",ze="sveltekit:scroll",Xe="sveltekit:states",Ot="sveltekit:pageurl",B="sveltekit:history",Y="sveltekit:navigation",j={tap:1,hover:2,viewport:3,eager:4,off:-1,false:-1},Z=location.origin;function Ue(e){if(e instanceof URL)return e;let t=document.baseURI;if(!t){const n=document.getElementsByTagName("base");t=n.length?n[0].href:document.URL}return new URL(e,t)}function fe(){return{x:pageXOffset,y:pageYOffset}}function V(e,t){return e.getAttribute(`data-sveltekit-${t}`)}const Be={...j,"":j.hover};function Ze(e){let t=e.assignedSlot??e.parentNode;return t?.nodeType===11&&(t=t.host),t}function Qe(e,t){for(;e&&e!==t;){if(e.nodeName.toUpperCase()==="A"&&e.hasAttribute("href"))return e;e=Ze(e)}}function ve(e,t,n){let r;try{if(r=new URL(e instanceof SVGAElement?e.href.baseVal:e.href,document.baseURI),n&&r.hash.match(/^#[^/]/)){const o=location.hash.split("#")[1]||"/";r.hash=`#${o}${r.hash}`}}catch{}const a=e instanceof SVGAElement?e.target.baseVal:e.target,s=!r||!!a||ue(r,t,n)||(e.getAttribute("rel")||"").split(/\s+/).includes("external"),i=r?.origin===Z&&e.hasAttribute("download");return{url:r,external:s,target:a,download:i}}function te(e){let t=null,n=null,r=null,a=null,s=null,i=null,o=e;for(;o&&o!==document.documentElement;)r===null&&(r=V(o,"preload-code")),a===null&&(a=V(o,"preload-data")),t===null&&(t=V(o,"keepfocus")),n===null&&(n=V(o,"noscroll")),s===null&&(s=V(o,"reload")),i===null&&(i=V(o,"replacestate")),o=Ze(o);function c(f){switch(f){case"":case"true":return!0;case"off":case"false":return!1;default:return}}return{preload_code:Be[r??"off"],preload_data:Be[a??"off"],keepfocus:c(t),noscroll:c(n),reload:c(s),replace_state:c(i)}}function Fe(e){const t=Ee(e);let n=!0;function r(){n=!0,t.update(i=>i)}function a(i){n=!1,t.set(i)}function s(i){let o;return t.subscribe(c=>{(o===void 0||n&&c!==o)&&i(o=c)})}return{notify:r,set:a,subscribe:s}}const et={v:()=>{}};function Nt(){const{set:e,subscribe:t}=Ee(!1);let n;async function r(){clearTimeout(n);try{const a=await fetch(`${Pt}/_app/version.json`,{headers:{pragma:"no-cache","cache-control":"no-cache"}});if(!a.ok)return!1;const i=(await a.json()).version!==Ct;return i&&(e(!0),et.v(),clearTimeout(n)),i}catch{return!1}}return{subscribe:t,check:r}}function ue(e,t,n){return e.origin!==Z||!e.pathname.startsWith(t)?!0:n?!(e.pathname===t+"/"||e.pathname===t+"/index.html"||e.protocol==="file:"&&e.pathname.replace(/\/[^/]+\.html?$/,"")===t):!1}function vn(e){}function qe(e){const t=$t(e),n=new ArrayBuffer(t.length),r=new DataView(n);for(let a=0;a<n.byteLength;a++)r.setUint8(a,t.charCodeAt(a));return n}const jt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function $t(e){e.length%4===0&&(e=e.replace(/==?$/,""));let t="",n=0,r=0;for(let a=0;a<e.length;a++)n<<=6,n|=jt.indexOf(e[a]),r+=6,r===24&&(t+=String.fromCharCode((n&16711680)>>16),t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255),n=r=0);return r===12?(n>>=4,t+=String.fromCharCode(n)):r===18&&(n>>=2,t+=String.fromCharCode((n&65280)>>8),t+=String.fromCharCode(n&255)),t}const Dt=-1,Vt=-2,Bt=-3,Ft=-4,qt=-5,Mt=-6;function Gt(e,t){if(typeof e=="number")return a(e,!0);if(!Array.isArray(e)||e.length===0)throw new Error("Invalid input");const n=e,r=Array(n.length);function a(s,i=!1){if(s===Dt)return;if(s===Bt)return NaN;if(s===Ft)return 1/0;if(s===qt)return-1/0;if(s===Mt)return-0;if(i)throw new Error("Invalid input");if(s in r)return r[s];const o=n[s];if(!o||typeof o!="object")r[s]=o;else if(Array.isArray(o))if(typeof o[0]=="string"){const c=o[0],f=t?.[c];if(f)return r[s]=f(a(o[1]));switch(c){case"Date":r[s]=new Date(o[1]);break;case"Set":const d=new Set;r[s]=d;for(let l=1;l<o.length;l+=1)d.add(a(o[l]));break;case"Map":const h=new Map;r[s]=h;for(let l=1;l<o.length;l+=2)h.set(a(o[l]),a(o[l+1]));break;case"RegExp":r[s]=new RegExp(o[1],o[2]);break;case"Object":r[s]=Object(o[1]);break;case"BigInt":r[s]=BigInt(o[1]);break;case"null":const u=Object.create(null);r[s]=u;for(let l=1;l<o.length;l+=2)u[o[l]]=a(o[l+1]);break;case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":{const l=globalThis[c],p=o[1],_=qe(p),m=new l(_);r[s]=m;break}case"ArrayBuffer":{const l=o[1],p=qe(l);r[s]=p;break}default:throw new Error(`Unknown type ${c}`)}}else{const c=new Array(o.length);r[s]=c;for(let f=0;f<o.length;f+=1){const d=o[f];d!==Vt&&(c[f]=a(d))}}else{const c={};r[s]=c;for(const f in o){const d=o[f];c[f]=a(d)}}return r[s]}return a(0)}const tt=new Set(["load","prerender","csr","ssr","trailingSlash","config"]);[...tt];const Ht=new Set([...tt]);[...Ht];function Kt(e){return e.filter(t=>t!=null)}const Wt="x-sveltekit-invalidated",Yt="x-sveltekit-trailing-slash";function ne(e){return e instanceof le||e instanceof Re?e.status:500}function Jt(e){return e instanceof Re?e.text:"Internal Error"}let E,J,ye;const zt=De.toString().includes("$$")||/function \w+\(\) \{\}/.test(De.toString());zt?(E={data:{},form:null,error:null,params:{},route:{id:null},state:{},status:-1,url:new URL("https://example.com")},J={current:null},ye={current:!1}):(E=new class{#e=T({});get data(){return x(this.#e)}set data(t){P(this.#e,t)}#t=T(null);get form(){return x(this.#t)}set form(t){P(this.#t,t)}#n=T(null);get error(){return x(this.#n)}set error(t){P(this.#n,t)}#r=T({});get params(){return x(this.#r)}set params(t){P(this.#r,t)}#a=T({id:null});get route(){return x(this.#a)}set route(t){P(this.#a,t)}#o=T({});get state(){return x(this.#o)}set state(t){P(this.#o,t)}#s=T(-1);get status(){return x(this.#s)}set status(t){P(this.#s,t)}#i=T(new URL("https://example.com"));get url(){return x(this.#i)}set url(t){P(this.#i,t)}},J=new class{#e=T(null);get current(){return x(this.#e)}set current(t){P(this.#e,t)}},ye=new class{#e=T(!1);get current(){return x(this.#e)}set current(t){P(this.#e,t)}},et.v=()=>ye.current=!0);function Xt(e){Object.assign(E,e)}const Zt="/__data.json",Qt=".html__data.json";function en(e){return e.endsWith(".html")?e.replace(/\.html$/,Qt):e.replace(/\/$/,"")+Zt}const{tick:tn}=mt,nn=new Set(["icon","shortcut icon","apple-touch-icon"]),D=Ye(ze)??{},z=Ye(Je)??{},N={url:Fe({}),page:Fe({}),navigating:Ee(null),updated:Nt()};function Le(e){D[e]=fe()}function rn(e,t){let n=e+1;for(;D[n];)delete D[n],n+=1;for(n=t+1;z[n];)delete z[n],n+=1}function q(e){return location.href=e.href,new Promise(()=>{})}async function nt(){if("serviceWorker"in navigator){const e=await navigator.serviceWorker.getRegistration(U||"/");e&&await e.update()}}function Me(){}let Te,be,re,C,Ae,v;const ae=[],oe=[];let O=null;const ee=new Map,rt=new Set,an=new Set,H=new Set;let w={branch:[],error:null,url:null},xe=!1,se=!1,Ge=!0,X=!1,M=!1,at=!1,Pe=!1,ot,A,I,$;const K=new Set;async function En(e,t,n){document.URL!==location.href&&(location.href=location.href),v=e,await e.hooks.init?.(),Te=xt(e),C=document.documentElement,Ae=t,be=e.nodes[0],re=e.nodes[1],be(),re(),A=history.state?.[B],I=history.state?.[Y],A||(A=I=Date.now(),history.replaceState({...history.state,[B]:A,[Y]:I},""));const r=D[A];function a(){r&&(history.scrollRestoration="manual",scrollTo(r.x,r.y))}n?(a(),await gn(Ae,n)):(await W({type:"enter",url:Ue(v.hash?_n(new URL(location.href)):location.href),replace_state:!0}),a()),pn()}function on(){ae.length=0,Pe=!1}function st(e){oe.some(t=>t?.snapshot)&&(z[e]=oe.map(t=>t?.snapshot?.capture()))}function it(e){z[e]?.forEach((t,n)=>{oe[n]?.snapshot?.restore(t)})}function He(){Le(A),Ve(ze,D),st(I),Ve(Je,z)}async function Ce(e,t,n,r){return W({type:"goto",url:Ue(e),keepfocus:t.keepFocus,noscroll:t.noScroll,replace_state:t.replaceState,state:t.state,redirect_count:n,nav_token:r,accept:()=>{t.invalidateAll&&(Pe=!0),t.invalidate&&t.invalidate.forEach(hn)}})}async function sn(e){if(e.id!==O?.id){const t={};K.add(t),O={id:e.id,token:t,promise:ft({...e,preload:t}).then(n=>(K.delete(t),n.type==="loaded"&&n.state.error&&(O=null),n))}}return O.promise}async function we(e){const t=(await he(e,!1))?.route;t&&await Promise.all([...t.layouts,t.leaf].map(n=>n?.[1]()))}function ct(e,t,n){w=e.state;const r=document.querySelector("style[data-sveltekit]");if(r&&r.remove(),Object.assign(E,e.props.page),ot=new v.root({target:t,props:{...e.props,stores:N,components:oe},hydrate:n,sync:!1}),it(I),n){const a={from:null,to:{params:w.params,route:{id:w.route?.id??null},url:new URL(location.href)},willUnload:!1,type:"enter",complete:Promise.resolve()};H.forEach(s=>s(a))}se=!0}function ie({url:e,params:t,branch:n,status:r,error:a,route:s,form:i}){let o="never";if(U&&(e.pathname===U||e.pathname===U+"/"))o="always";else for(const l of n)l?.slash!==void 0&&(o=l.slash);e.pathname=_t(e.pathname,o),e.search=e.search;const c={type:"loaded",state:{url:e,params:t,branch:n,error:a,route:s},props:{constructors:Kt(n).map(l=>l.node.component),page:$e(E)}};i!==void 0&&(c.props.form=i);let f={},d=!E,h=0;for(let l=0;l<Math.max(n.length,w.branch.length);l+=1){const p=n[l],_=w.branch[l];p?.data!==_?.data&&(d=!0),p&&(f={...f,...p.data},d&&(c.props[`data_${h}`]=f),h+=1)}return(!w.url||e.href!==w.url.href||w.error!==a||i!==void 0&&i!==E.form||d)&&(c.props.page={error:a,params:t,route:{id:s?.id??null},state:{},status:r,url:new URL(e),form:i??null,data:d?f:E.data}),c}async function Oe({loader:e,parent:t,url:n,params:r,route:a,server_data_node:s}){let i=null,o=!0;const c={dependencies:new Set,params:new Set,parent:!1,route:!1,url:!1,search_params:new Set},f=await e();if(f.universal?.load){let d=function(...u){for(const l of u){const{href:p}=new URL(l,n);c.dependencies.add(p)}};const h={route:new Proxy(a,{get:(u,l)=>(o&&(c.route=!0),u[l])}),params:new Proxy(r,{get:(u,l)=>(o&&c.params.add(l),u[l])}),data:s?.data??null,url:vt(n,()=>{o&&(c.url=!0)},u=>{o&&c.search_params.add(u)},v.hash),async fetch(u,l){u instanceof Request&&(l={body:u.method==="GET"||u.method==="HEAD"?void 0:await u.blob(),cache:u.cache,credentials:u.credentials,headers:[...u.headers].length>0?u?.headers:void 0,integrity:u.integrity,keepalive:u.keepalive,method:u.method,mode:u.mode,redirect:u.redirect,referrer:u.referrer,referrerPolicy:u.referrerPolicy,signal:u.signal,...l});const{resolved:p,promise:_}=lt(u,l,n);return o&&d(p.href),_},setHeaders:()=>{},depends:d,parent(){return o&&(c.parent=!0),t()},untrack(u){o=!1;try{return u()}finally{o=!0}}};i=await f.universal.load.call(null,h)??null}return{node:f,loader:e,server:s,universal:f.universal?.load?{type:"data",data:i,uses:c}:null,data:i??s?.data??null,slash:f.universal?.trailingSlash??s?.slash}}function lt(e,t,n){let r=e instanceof Request?e.url:e;const a=new URL(r,n);a.origin===n.origin&&(r=a.href.slice(n.origin.length));const s=se?St(r,a.href,t):Et(r,t);return{resolved:a,promise:s}}function Ke(e,t,n,r,a,s){if(Pe)return!0;if(!a)return!1;if(a.parent&&e||a.route&&t||a.url&&n)return!0;for(const i of a.search_params)if(r.has(i))return!0;for(const i of a.params)if(s[i]!==w.params[i])return!0;for(const i of a.dependencies)if(ae.some(o=>o(new URL(i))))return!0;return!1}function Ne(e,t){return e?.type==="data"?e:e?.type==="skip"?t??null:null}function cn(e,t){if(!e)return new Set(t.searchParams.keys());const n=new Set([...e.searchParams.keys(),...t.searchParams.keys()]);for(const r of n){const a=e.searchParams.getAll(r),s=t.searchParams.getAll(r);a.every(i=>s.includes(i))&&s.every(i=>a.includes(i))&&n.delete(r)}return n}function We({error:e,url:t,route:n,params:r}){return{type:"loaded",state:{error:e,url:t,route:n,params:r,branch:[]},props:{page:$e(E),constructors:[]}}}async function ft({id:e,invalidating:t,url:n,params:r,route:a,preload:s}){if(O?.id===e)return K.delete(O.token),O.promise;const{errors:i,layouts:o,leaf:c}=a,f=[...o,c];i.forEach(g=>g?.().catch(()=>{})),f.forEach(g=>g?.[1]().catch(()=>{}));let d=null;const h=w.url?e!==ce(w.url):!1,u=w.route?a.id!==w.route.id:!1,l=cn(w.url,n);let p=!1;const _=f.map((g,y)=>{const b=w.branch[y],k=!!g?.[0]&&(b?.loader!==g[1]||Ke(p,u,h,l,b.server?.uses,r));return k&&(p=!0),k});if(_.some(Boolean)){try{d=await ht(n,_)}catch(g){const y=await F(g,{url:n,params:r,route:{id:e}});return K.has(s)?We({error:y,url:n,params:r,route:a}):de({status:ne(g),error:y,url:n,route:a})}if(d.type==="redirect")return d}const m=d?.nodes;let R=!1;const S=f.map(async(g,y)=>{if(!g)return;const b=w.branch[y],k=m?.[y];if((!k||k.type==="skip")&&g[1]===b?.loader&&!Ke(R,u,h,l,b.universal?.uses,r))return b;if(R=!0,k?.type==="error")throw k;return Oe({loader:g[1],url:n,params:r,route:a,parent:async()=>{const pe={};for(let ge=0;ge<y;ge+=1)Object.assign(pe,(await S[ge])?.data);return pe},server_data_node:Ne(k===void 0&&g[0]?{type:"skip"}:k??null,g[0]?b?.server:void 0)})});for(const g of S)g.catch(()=>{});const L=[];for(let g=0;g<f.length;g+=1)if(f[g])try{L.push(await S[g])}catch(y){if(y instanceof Se)return{type:"redirect",location:y.location};if(K.has(s))return We({error:await F(y,{params:r,url:n,route:{id:a.id}}),url:n,params:r,route:a});let b=ne(y),k;if(m?.includes(y))b=y.status??b,k=y.error;else if(y instanceof le)k=y.body;else{if(await N.updated.check())return await nt(),await q(n);k=await F(y,{params:r,url:n,route:{id:a.id}})}const Q=await ln(g,L,i);return Q?ie({url:n,params:r,branch:L.slice(0,Q.idx).concat(Q.node),status:b,error:k,route:a}):await dt(n,{id:a.id},k,b)}else L.push(void 0);return ie({url:n,params:r,branch:L,status:200,error:null,route:a,form:t?void 0:null})}async function ln(e,t,n){for(;e--;)if(n[e]){let r=e;for(;!t[r];)r-=1;try{return{idx:r+1,node:{node:await n[e](),loader:n[e],data:{},server:null,universal:null}}}catch{continue}}}async function de({status:e,error:t,url:n,route:r}){const a={};let s=null;if(v.server_loads[0]===0)try{const o=await ht(n,[!0]);if(o.type!=="data"||o.nodes[0]&&o.nodes[0].type!=="data")throw 0;s=o.nodes[0]??null}catch{(n.origin!==Z||n.pathname!==location.pathname||xe)&&await q(n)}try{const o=await Oe({loader:be,url:n,params:a,route:r,parent:()=>Promise.resolve({}),server_data_node:Ne(s)}),c={node:await re(),loader:re,universal:null,server:null,data:null};return ie({url:n,params:a,branch:[o,c],status:e,error:t,route:null})}catch(o){if(o instanceof Se)return Ce(new URL(o.location,location.href),{},0);throw o}}async function fn(e){const t=e.href;if(ee.has(t))return ee.get(t);let n;try{const r=(async()=>{let a=await v.hooks.reroute({url:new URL(e),fetch:async(s,i)=>lt(s,i,e).promise})??e;if(typeof a=="string"){const s=new URL(e);v.hash?s.hash=a:s.pathname=a,a=s}return a})();ee.set(t,r),n=await r}catch{ee.delete(t);return}return n}async function he(e,t){if(e&&!ue(e,U,v.hash)){const n=await fn(e);if(!n)return;const r=un(n);for(const a of Te){const s=a.exec(r);if(s)return{id:ce(e),invalidating:t,route:a,params:wt(s),url:e}}}}function un(e){return yt(v.hash?e.hash.replace(/^#/,"").replace(/[?#].+/,""):e.pathname.slice(U.length))||"/"}function ce(e){return(v.hash?e.hash.replace(/^#/,""):e.pathname)+e.search}function ut({url:e,type:t,intent:n,delta:r}){let a=!1;const s=je(w,n,e,t);r!==void 0&&(s.navigation.delta=r);const i={...s.navigation,cancel:()=>{a=!0,s.reject(new Error("navigation cancelled"))}};return X||rt.forEach(o=>o(i)),a?null:s}async function W({type:e,url:t,popped:n,keepfocus:r,noscroll:a,replace_state:s,state:i={},redirect_count:o=0,nav_token:c={},accept:f=Me,block:d=Me}){const h=$;$=c;const u=await he(t,!1),l=e==="enter"?je(w,u,t,e):ut({url:t,type:e,delta:n?.delta,intent:u});if(!l){d(),$===c&&($=h);return}const p=A,_=I;f(),X=!0,se&&l.navigation.type!=="enter"&&N.navigating.set(J.current=l.navigation);let m=u&&await ft(u);if(!m){if(ue(t,U,v.hash))return await q(t);m=await dt(t,{id:null},await F(new Re(404,"Not Found",`Not found: ${t.pathname}`),{url:t,params:{},route:{id:null}}),404)}if(t=u?.url||t,$!==c)return l.reject(new Error("navigation aborted")),!1;if(m.type==="redirect")if(o>=20)m=await de({status:500,error:await F(new Error("Redirect loop"),{url:t,params:{},route:{id:null}}),url:t,route:{id:null}});else return await Ce(new URL(m.location,t).href,{},o+1,c),!1;else m.props.page.status>=400&&await N.updated.check()&&(await nt(),await q(t));if(on(),Le(p),st(_),m.props.page.url.pathname!==t.pathname&&(t.pathname=m.props.page.url.pathname),i=n?n.state:i,!n){const g=s?0:1,y={[B]:A+=g,[Y]:I+=g,[Xe]:i};(s?history.replaceState:history.pushState).call(history,y,"",t),s||rn(A,I)}if(O=null,m.props.page.state=i,se){w=m.state,m.props.page&&(m.props.page.url=t);const g=(await Promise.all(Array.from(an,y=>y(l.navigation)))).filter(y=>typeof y=="function");if(g.length>0){let y=function(){g.forEach(b=>{H.delete(b)})};g.push(y),g.forEach(b=>{H.add(b)})}ot.$set(m.props),Xt(m.props.page),at=!0}else ct(m,Ae,!1);const{activeElement:R}=document;await tn();const S=n?n.scroll:a?fe():null;if(Ge){const g=t.hash&&document.getElementById(gt(t));S?scrollTo(S.x,S.y):g?g.scrollIntoView():scrollTo(0,0)}const L=document.activeElement!==R&&document.activeElement!==document.body;!r&&!L&&mn(t),Ge=!0,m.props.page&&Object.assign(E,m.props.page),X=!1,e==="popstate"&&it(I),l.fulfil(void 0),H.forEach(g=>g(l.navigation)),N.navigating.set(J.current=null)}async function dt(e,t,n,r){return e.origin===Z&&e.pathname===location.pathname&&!xe?await de({status:r,error:n,url:e,route:t}):await q(e)}function dn(){let e,t,n;C.addEventListener("mousemove",o=>{const c=o.target;clearTimeout(e),e=setTimeout(()=>{s(c,j.hover)},20)});function r(o){o.defaultPrevented||s(o.composedPath()[0],j.tap)}C.addEventListener("mousedown",r),C.addEventListener("touchstart",r,{passive:!0});const a=new IntersectionObserver(o=>{for(const c of o)c.isIntersecting&&(we(new URL(c.target.href)),a.unobserve(c.target))},{threshold:0});async function s(o,c){const f=Qe(o,C),d=f===t&&c>=n;if(!f||d)return;const{url:h,external:u,download:l}=ve(f,U,v.hash);if(u||l)return;const p=te(f),_=h&&ce(w.url)===ce(h);if(!(p.reload||_))if(c<=p.preload_data){t=f,n=j.tap;const m=await he(h,!1);if(!m)return;sn(m)}else c<=p.preload_code&&(t=f,n=c,we(h))}function i(){a.disconnect();for(const o of C.querySelectorAll("a")){const{url:c,external:f,download:d}=ve(o,U,v.hash);if(f||d)continue;const h=te(o);h.reload||(h.preload_code===j.viewport&&a.observe(o),h.preload_code===j.eager&&we(c))}}H.add(i),i()}function F(e,t){if(e instanceof le)return e.body;const n=ne(e),r=Jt(e);return v.hooks.handleError({error:e,event:t,status:n,message:r})??{message:r}}function Sn(e,t={}){return e=new URL(Ue(e)),e.origin!==Z?Promise.reject(new Error("goto: invalid URL")):Ce(e,t,0)}function hn(e){if(typeof e=="function")ae.push(e);else{const{href:t}=new URL(e,location.href);ae.push(n=>n.href===t)}}function pn(){history.scrollRestoration="manual",addEventListener("beforeunload",t=>{let n=!1;if(He(),!X){const r=je(w,void 0,null,"leave"),a={...r.navigation,cancel:()=>{n=!0,r.reject(new Error("navigation cancelled"))}};rt.forEach(s=>s(a))}n?(t.preventDefault(),t.returnValue=""):history.scrollRestoration="auto"}),addEventListener("visibilitychange",()=>{document.visibilityState==="hidden"&&He()}),navigator.connection?.saveData||dn(),C.addEventListener("click",async t=>{if(t.button||t.which!==1||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.defaultPrevented)return;const n=Qe(t.composedPath()[0],C);if(!n)return;const{url:r,external:a,target:s,download:i}=ve(n,U,v.hash);if(!r)return;if(s==="_parent"||s==="_top"){if(window.parent!==window)return}else if(s&&s!=="_self")return;const o=te(n);if(!(n instanceof SVGAElement)&&r.protocol!==location.protocol&&!(r.protocol==="https:"||r.protocol==="http:")||i)return;const[f,d]=(v.hash?r.hash.replace(/^#/,""):r.href).split("#"),h=f===me(location);if(a||o.reload&&(!h||!d)){ut({url:r,type:"link"})?X=!0:t.preventDefault();return}if(d!==void 0&&h){const[,u]=w.url.href.split("#");if(u===d){if(t.preventDefault(),d===""||d==="top"&&n.ownerDocument.getElementById("top")===null)window.scrollTo({top:0});else{const l=n.ownerDocument.getElementById(decodeURIComponent(d));l&&(l.scrollIntoView(),l.focus())}return}if(M=!0,Le(A),e(r),!o.replace_state)return;M=!1}t.preventDefault(),await new Promise(u=>{requestAnimationFrame(()=>{setTimeout(u,0)}),setTimeout(u,100)}),await W({type:"link",url:r,keepfocus:o.keepfocus,noscroll:o.noscroll,replace_state:o.replace_state??r.href===location.href})}),C.addEventListener("submit",t=>{if(t.defaultPrevented)return;const n=HTMLFormElement.prototype.cloneNode.call(t.target),r=t.submitter;if((r?.formTarget||n.target)==="_blank"||(r?.formMethod||n.method)!=="get")return;const i=new URL(r?.hasAttribute("formaction")&&r?.formAction||n.action);if(ue(i,U,!1))return;const o=t.target,c=te(o);if(c.reload)return;t.preventDefault(),t.stopPropagation();const f=new FormData(o),d=r?.getAttribute("name");d&&f.append(d,r?.getAttribute("value")??""),i.search=new URLSearchParams(f).toString(),W({type:"form",url:i,keepfocus:c.keepfocus,noscroll:c.noscroll,replace_state:c.replace_state??i.href===location.href})}),addEventListener("popstate",async t=>{if(!ke){if(t.state?.[B]){const n=t.state[B];if($={},n===A)return;const r=D[n],a=t.state[Xe]??{},s=new URL(t.state[Ot]??location.href),i=t.state[Y],o=w.url?me(location)===me(w.url):!1;if(i===I&&(at||o)){a!==E.state&&(E.state=a),e(s),D[A]=fe(),r&&scrollTo(r.x,r.y),A=n;return}const f=n-A;await W({type:"popstate",url:s,popped:{state:a,scroll:r,delta:f},accept:()=>{A=n,I=i},block:()=>{history.go(-f)},nav_token:$})}else if(!M){const n=new URL(location.href);e(n),v.hash&&location.reload()}}}),addEventListener("hashchange",()=>{M&&(M=!1,history.replaceState({...history.state,[B]:++A,[Y]:I},"",location.href))});for(const t of document.querySelectorAll("link"))nn.has(t.rel)&&(t.href=t.href);addEventListener("pageshow",t=>{t.persisted&&N.navigating.set(J.current=null)});function e(t){w.url=E.url=t,N.page.set($e(E)),N.page.notify()}}async function gn(e,{status:t=200,error:n,node_ids:r,params:a,route:s,server_route:i,data:o,form:c}){xe=!0;const f=new URL(location.href);let d;({params:a={},route:s={id:null}}=await he(f,!1)||{}),d=Te.find(({id:l})=>l===s.id);let h,u=!0;try{const l=r.map(async(_,m)=>{const R=o[m];return R?.uses&&(R.uses=pt(R.uses)),Oe({loader:v.nodes[_],url:f,params:a,route:s,parent:async()=>{const S={};for(let L=0;L<m;L+=1)Object.assign(S,(await l[L]).data);return S},server_data_node:Ne(R)})}),p=await Promise.all(l);if(d){const _=d.layouts;for(let m=0;m<_.length;m++)_[m]||p.splice(m,0,void 0)}h=ie({url:f,params:a,branch:p,status:t,error:n,form:c,route:d??null})}catch(l){if(l instanceof Se){await q(new URL(l.location,location.href));return}h=await de({status:ne(l),error:await F(l,{url:f,params:a,route:s}),url:f,route:s}),e.textContent="",u=!1}h.props.page&&(h.props.page.state={}),ct(h,e,u)}async function ht(e,t){const n=new URL(e);n.pathname=en(e.pathname),e.pathname.endsWith("/")&&n.searchParams.append(Yt,"1"),n.searchParams.append(Wt,t.map(s=>s?"1":"0").join(""));const r=window.fetch,a=await r(n.href,{});if(!a.ok){let s;throw a.headers.get("content-type")?.includes("application/json")?s=await a.json():a.status===404?s="Not Found":a.status===500&&(s="Internal Error"),new le(a.status,s)}return new Promise(async s=>{const i=new Map,o=a.body.getReader(),c=new TextDecoder;function f(h){return Gt(h,{...v.decoders,Promise:u=>new Promise((l,p)=>{i.set(u,{fulfil:l,reject:p})})})}let d="";for(;;){const{done:h,value:u}=await o.read();if(h&&!d)break;for(d+=!u&&d?`
`:c.decode(u,{stream:!0});;){const l=d.indexOf(`
`);if(l===-1)break;const p=JSON.parse(d.slice(0,l));if(d=d.slice(l+1),p.type==="redirect")return s(p);if(p.type==="data")p.nodes?.forEach(_=>{_?.type==="data"&&(_.uses=pt(_.uses),_.data=f(_.data))}),s(p);else if(p.type==="chunk"){const{id:_,data:m,error:R}=p,S=i.get(_);i.delete(_),R?S.reject(f(R)):S.fulfil(f(m))}}}})}function pt(e){return{dependencies:new Set(e?.dependencies??[]),params:new Set(e?.params??[]),parent:!!e?.parent,route:!!e?.route,url:!!e?.url,search_params:new Set(e?.search_params??[])}}let ke=!1;function mn(e){const t=document.querySelector("[autofocus]");if(t)t.focus();else{const n=gt(e);if(n&&document.getElementById(n)){const{x:a,y:s}=fe();setTimeout(()=>{const i=history.state;ke=!0,location.replace(`#${n}`),v.hash&&location.replace(e.hash),history.replaceState(i,"",e.hash),scrollTo(a,s),ke=!1})}else{const a=document.body,s=a.getAttribute("tabindex");a.tabIndex=-1,a.focus({preventScroll:!0,focusVisible:!1}),s!==null?a.setAttribute("tabindex",s):a.removeAttribute("tabindex")}const r=getSelection();if(r&&r.type!=="None"){const a=[];for(let s=0;s<r.rangeCount;s+=1)a.push(r.getRangeAt(s));setTimeout(()=>{if(r.rangeCount===a.length){for(let s=0;s<r.rangeCount;s+=1){const i=a[s],o=r.getRangeAt(s);if(i.commonAncestorContainer!==o.commonAncestorContainer||i.startContainer!==o.startContainer||i.endContainer!==o.endContainer||i.startOffset!==o.startOffset||i.endOffset!==o.endOffset)return}r.removeAllRanges()}})}}}function je(e,t,n,r){let a,s;const i=new Promise((c,f)=>{a=c,s=f});return i.catch(()=>{}),{navigation:{from:{params:e.params,route:{id:e.route?.id??null},url:e.url},to:n&&{params:t?.params??null,route:{id:t?.route?.id??null},url:n},willUnload:!t,type:r,complete:i},fulfil:a,reject:s}}function $e(e){return{data:e.data,error:e.error,form:e.form,params:e.params,route:e.route,state:e.state,status:e.status,url:e.url}}function _n(e){const t=new URL(e);return t.hash=decodeURIComponent(e.hash),t}function gt(e){let t;if(v.hash){const[,,n]=e.hash.split("#",3);t=n??""}else t=e.hash.slice(1);return decodeURIComponent(t)}export{En as a,Sn as g,vn as l,E as p,N as s};
