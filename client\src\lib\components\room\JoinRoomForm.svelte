<script lang="ts">
  import { socketClient } from "$lib/socket";
  import { roomState, roomActions, RoomStatus } from "$lib/stores/roomState";

  interface Props {
    gameId: string;
    onCancel?: () => void;
  }

  let { gameId, onCancel }: Props = $props();

  let roomCode = $state("");
  let isSubmitting = $state(false);
  let error = $state<string | null>(null);

  // Watch room state for errors and loading
  $effect(() => {
    if ($roomState.error) {
      error = $roomState.error;
      isSubmitting = false;
      // Clear the room error after showing it
      setTimeout(() => {
        roomActions.clearError();
      }, 100);
    }

    if ($roomState.status === RoomStatus.Joining) {
      isSubmitting = true;
      error = null;
    } else if ($roomState.status === RoomStatus.InRoom) {
      // Successfully joined, form will be hidden by parent
      isSubmitting = false;
      error = null;
    } else if ($roomState.status === RoomStatus.NotInRoom && isSubmitting) {
      // Failed to join
      isSubmitting = false;
    }
  });

  function handleSubmit() {
    if (!roomCode.trim()) {
      error = "Please enter a room code";
      return;
    }

    if (roomCode.trim().length < 3) {
      error = "Room code must be at least 3 characters";
      return;
    }

    error = null;
    isSubmitting = true;

    try {
      socketClient.joinRoomByCode(roomCode.trim().toUpperCase(), gameId);
    } catch (err) {
      error = "Failed to join room. Please try again.";
      isSubmitting = false;
    }
  }

  function handleCancel() {
    if (onCancel) {
      onCancel();
    }
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === "Enter") {
      handleSubmit();
    } else if (event.key === "Escape") {
      handleCancel();
    }
  }

  // Auto-format room code as user types
  function formatRoomCode(value: string): string {
    return value
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, "")
      .slice(0, 8);
  }

  function handleInput(event: Event) {
    const target = event.target as HTMLInputElement;
    roomCode = formatRoomCode(target.value);
    error = null; // Clear error when user starts typing
  }
</script>

<div class="join-room-form">
  <button class="back-button" onclick={onCancel}>
    <svg viewBox="0 0 20 20" fill="currentColor">
      <path
        fill-rule="evenodd"
        d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
        clip-rule="evenodd"
      />
    </svg>
    Back
  </button>

  <div class="form-header">
    <h3 class="form-title">Join Room</h3>
    <p class="form-subtitle">Enter the room code to join a multiplayer game</p>
  </div>

  <div class="form-content">
    <div class="input-group">
      <label for="room-code" class="input-label">Room Code</label>
      <input
        id="room-code"
        type="text"
        bind:value={roomCode}
        oninput={handleInput}
        onkeydown={handleKeydown}
        placeholder="Enter room code..."
        class="room-code-input"
        class:error
        disabled={isSubmitting}
        autocomplete="off"
        spellcheck="false"
      />
      {#if error}
        <div class="error-message">{error}</div>
      {/if}
    </div>

    <div class="form-actions">
      <button
        type="button"
        onclick={handleSubmit}
        class="join-button"
        disabled={isSubmitting || !roomCode.trim()}
      >
        {#if isSubmitting}
          <div class="loading-spinner"></div>
          Joining...
        {:else}
          Join Room
        {/if}
      </button>
    </div>
  </div>
</div>

<style>
  .join-room-form {
    background: rgba(0, 0, 0, 0.4);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    max-width: 400px;
    width: 100%;
  }

  .form-header {
    text-align: center;
    margin-bottom: 24px;
  }

  .form-title {
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-bottom: 8px;
  }

  .form-subtitle {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.4;
  }

  .form-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .input-label {
    font-size: 14px;
    font-weight: 600;
    color: white;
  }

  .room-code-input {
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    letter-spacing: 2px;
    text-transform: uppercase;
    transition: all 0.2s ease;
  }

  .room-code-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
    text-transform: none;
    letter-spacing: normal;
    font-weight: normal;
  }

  .room-code-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    background: rgba(255, 255, 255, 0.15);
  }

  .room-code-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .room-code-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
  }

  .error-message {
    color: #ef4444;
    font-size: 12px;
    font-weight: 500;
    margin-top: 4px;
  }

  .form-actions {
    display: flex;
    gap: 12px;
  }

  .back-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
  }

  .back-button:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .back-button svg {
    width: 16px;
    height: 16px;
  }

  .join-button {
    flex: 1;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .join-button {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .join-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
  }

  .join-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Mobile responsiveness */
  @media (max-width: 480px) {
    .join-room-form {
      padding: 20px;
      /* margin: 16px; */
    }

    .form-actions {
      flex-direction: column;
    }

    .join-button {
      width: 100%;
    }
  }
</style>
