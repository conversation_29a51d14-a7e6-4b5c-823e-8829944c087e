{"version": 3, "file": "index2-B_5xEZv0.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index2.js"], "sourcesContent": ["import \"./state.svelte.js\";\nimport \"@sveltejs/kit/internal\";\nimport { w as writable } from \"./exports.js\";\nimport { U as getContext } from \"./index.js\";\nfunction create_updated_store() {\n  const { set, subscribe } = writable(false);\n  {\n    return {\n      subscribe,\n      // eslint-disable-next-line @typescript-eslint/require-await\n      check: async () => false\n    };\n  }\n}\nconst stores = {\n  updated: /* @__PURE__ */ create_updated_store()\n};\n({\n  check: stores.updated.check\n});\nfunction context() {\n  return getContext(\"__request__\");\n}\nconst page$1 = {\n  get error() {\n    return context().page.error;\n  },\n  get params() {\n    return context().page.params;\n  },\n  get status() {\n    return context().page.status;\n  },\n  get url() {\n    return context().page.url;\n  }\n};\nconst page = page$1;\nexport {\n  page as p\n};\n"], "names": [], "mappings": ";;;AAIA,SAAS,oBAAoB,GAAG;AAChC,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC;AAC5C,EAAE;AACF,IAAI,OAAO;AACX,MAAM,SAAS;AACf;AACA,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,EAAE;AACF;AACA,MAAM,MAAM,GAAG;AACf,EAAE,OAAO,kBAAkB,oBAAoB;AAC/C,CAAC;AACD,CAAC;AACD,EAAE,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC;AACxB,CAAC;AACD,SAAS,OAAO,GAAG;AACnB,EAAE,OAAO,UAAU,CAAC,aAAa,CAAC;AAClC;AACA,MAAM,MAAM,GAAG;AACf,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,KAAK;AAC/B,EAAE,CAAC;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM;AAChC,EAAE,CAAC;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM;AAChC,EAAE,CAAC;AACH,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG;AAC7B,EAAE;AACF,CAAC;AACI,MAAC,IAAI,GAAG;;;;"}