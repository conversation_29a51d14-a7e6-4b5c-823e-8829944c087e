import type { SocketClient } from '$lib/socket';
import MumsNumbers from './MumsNumbers.svelte';
import { mount, unmount } from 'svelte';

// NumberConnect game integration
export interface MumsNumbersConfig {
  gameId: string;
  containerId: string;
  socketClient?: SocketClient;
  roomId?: string;

  onScoreUpdate?: (score: number) => void;
  onGameComplete?: (finalScore: number) => void;
}

export class MumsNumbersGame {
  private config: MumsNumbersConfig;
  private container: HTMLElement | null = null;
  private svelteComponent: any = null;
  private currentScore: number = 0;

  constructor(config: MumsNumbersConfig) {
    this.config = config;
  }

  async init(): Promise<void> {
    // Initialize NumberConnect game instance
    console.log('Initializing NumberConnect game...');

    this.container = document.getElementById(this.config.containerId);
    if (!this.container) {
      throw new Error(`Container element with ID "${this.config.containerId}" not found`);
    }

    // Clear container
    this.container.innerHTML = '';

    // Create the Svelte component
    this.svelteComponent = mount(MumsNumbers, {
      target: this.container,
      props: {
        socketClient: this.config.socketClient,
        gameId: this.config.gameId,
        containerId: this.config.containerId,
        onScoreUpdate: (score: number) => {
          this.currentScore = score;
          this.config.onScoreUpdate?.(score);
        },
        onGameComplete: this.config.onGameComplete
      }
    });

    console.log('NumberConnect game initialized successfully');
  }
  
  start(): void {
    console.log('Starting NumberConnect game...');

    // Send initialization request to server
    if (this.config.socketClient && this.config.socketClient.isConnected()) {
      this.config.socketClient.initGame();
    } else {
      console.error("No server connection available - cannot initialize game");
    }

    // Start the game component
    if (this.svelteComponent && this.svelteComponent.startGame) {
      this.svelteComponent.startGame();
    }
  }

  pause(): void {
    console.log('Pausing NumberConnect game...');
    // TODO: Implement pause functionality if needed
  }

  resume(): void {
    console.log('Resuming NumberConnect game...');
    // TODO: Implement resume functionality if needed
  }

  destroy(): void {
    console.log('Destroying NumberConnect game...');

    if (this.svelteComponent) {
      unmount(this.svelteComponent);
      this.svelteComponent = null;
    }

    if (this.container) {
      this.container.innerHTML = '';
    }
  }

  getCurrentScore(): number {
    return this.currentScore;
  }
}
