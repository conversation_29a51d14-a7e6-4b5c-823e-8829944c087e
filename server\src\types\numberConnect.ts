/**
 * NumberConnect Game Types and Interfaces
 * Server-side types for the NumberConnect game
 */

import type { BaseActionResult, GameActionData } from './game';

// ===== POSITION AND GEOMETRY TYPES =====

/**
 * 2D grid position coordinates (row, col)
 */
export interface Position {
  row: number;
  col: number;
}

/**
 * Grid cell data structure
 */
export interface GridCell {
  visited: boolean;
  number: number | null;
  hasPath: boolean;
}

/**
 * Generated level data structure
 */
export interface GeneratedLevel {
  grid: GridCell[][];
  solutionPath: Position[];
  numberPositions: { [key: number]: Position };
  maxNumber: number;
}

// ===== GAME STATE TYPES =====

/**
 * NumberConnect-specific game state
 */
export interface NumberConnectGameState {
  grid: GridCell[][];
  solutionPath: Position[];
  numberPositions: { [key: number]: Position };
  playerPath: Position[];
  currentNumber: number;
  moveCount: number;
  gameStarted: boolean;
  gameEnded: boolean;
  startTime: number | null; // overall game start
  currentLevel: number;
  levelsCompleted: number;
  totalScore: number;
  // Per-board timing
  boardStartTime: number | null;
  boardTimes: number[]; // ms per completed board
}

// ===== ACTION TYPES =====

/**
 * Path move action data
 */
export interface PathMoveData extends GameActionData {
  action: {
    type: 'path_move';
    data: {
      newCell?: Position;
      path?: Position[];
      isDrawing?: boolean;
      moveType?: 'start' | 'continue' | 'end';
      timestamp: number;
    };
  };
}

/**
 * Result of a path move action
 */
export interface PathMoveResult extends BaseActionResult {
  isValid: boolean;
  newPath: Position[];
  currentNumber: number;
  gameWon?: boolean;
  invalidReason?: string;
  levelCompleted?: boolean;
  newLevel?: boolean;
  currentLevel?: number;
  levelsCompleted?: number;
  boardTimeMs?: number; // per-board time for completed boards
  newPuzzle?: {
    grid: GridCell[][];
    numberPositions: { [key: number]: Position };
    solutionPath: Position[];
    maxNumber?: number;
  };
}

// ===== SOCKET EVENT DATA STRUCTURES =====

/**
 * NumberConnect game initialization data
 */
export interface NumberConnectGameInitData {
  gameState: {
    score: number;
    lives: number;
    isActive: boolean;
    startTime?: number;
  };
  puzzleState: {
    grid: GridCell[][];
    numberPositions: { [key: number]: Position };
    currentNumber: number;
    playerPath: Position[];
  } | null;
  message: string;
}

/**
 * NumberConnect game started data
 */
export interface NumberConnectGameStartedData {
  gameState: {
    score: number;
    lives: number;
    isActive: boolean;
    startTime?: number;
  };
  puzzleState: {
    grid: GridCell[][];
    numberPositions: { [key: number]: Position };
    currentNumber: number;
    playerPath: Position[];
  } | null;
  message: string;
}

/**
 * NumberConnect action result data
 */
export interface NumberConnectActionResultData {
  actionType: 'path_move';
  data: {
    isValid: boolean;
    newPath: Position[];
    currentNumber: number;
    points: number;
    newScore: number;
    newLives: number;
    gameEnded: boolean;
    gameWon?: boolean;
    invalidReason?: string;
    levelCompleted?: boolean;
    newLevel?: boolean;
    currentLevel?: number;
    levelsCompleted?: number;
    boardTimeMs?: number;
    newPuzzle?: {
      grid: GridCell[][];
      numberPositions: { [key: number]: Position };
      solutionPath: Position[];
      maxNumber?: number;
    };
    puzzleState?: {
      grid: GridCell[][];
      numberPositions: { [key: number]: Position };
      currentNumber: number;
      playerPath: Position[];
    };
  };
}

// ===== CONSTANTS =====

/**
 * NumberConnect game configuration constants
 */
export const NUMBER_CONNECT_CONSTANTS = {
  GRID_SIZE: 5,
  MAX_NUMBER: 5,
  INITIAL_LIVES: 3,
  
  SCORING: {
    NUMBER_REACHED: 100,
    TIME_BONUS_MULTIPLIER: 2,
    LEVEL_COMPLETION_BONUS: 500,
    COMPLETION_BONUS: 1000
  },
  
  PENALTIES: {
    INVALID_MOVE: 1, // Lives deducted for invalid moves
    WRONG_PATH: 1
  }
} as const;
