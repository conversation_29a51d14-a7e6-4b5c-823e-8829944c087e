import { Server, Socket } from 'socket.io';
import { FingerFrenzy<PERSON>ontroller, BingoController, MatchingMayhemController, NumberSequenceController, NumberConnectController, FindingLuigiController } from '../controllers';
import gameService from '../services/gameService';
import { authMiddleware, AuthenticatedSocket, getAuthenticatedUser } from '../middleware/auth.js';
import { sessionService } from '../services/sessionService.js';
import { roomService } from '../services/roomService.js';
import { PlayerRole, RoomStatus } from '../types/room.js';
import { logger } from '../utils/logger.js';

// Initialize game controllers
const fingerFrenzyController = new FingerFrenzyController(gameService);
const bingoController = new BingoController(gameService);
const matchingMayhemController = new MatchingMayhemController(gameService);
const numberSequenceController = new NumberSequenceController(gameService);
const numberConnectController = new NumberConnectController(gameService);
const findingLuigiController = new FindingLuigiController(gameService);

export function setupSocketHandlers(io: Server) {
  // Provide io to sessionService for room broadcasts (leaderboard updates, etc.)
  sessionService.setIo(io);
  // Apply authentication middleware
  io.use(authMiddleware);

  io.on('connection', (socket: Socket) => {
    // After authentication middleware, socket has user property
    const authenticatedSocket = socket as AuthenticatedSocket;
    const user = getAuthenticatedUser(authenticatedSocket);
    if (!user) {
      logger.error('Authenticated socket missing user data', { socketId: authenticatedSocket.id });
      authenticatedSocket.disconnect();
      return;
    }

    logger.info(`Authenticated client connected`, {
      socketId: authenticatedSocket.id,
      userId: user.userId,
      gameId: user.gameId,
      roomId: user.roomId
    });

    // Create user session
    sessionService.createUserSession(user, authenticatedSocket.id);

    // Automatically join the user's designated room
    authenticatedSocket.join(user.roomId);

    // Setup game-specific handlers
    fingerFrenzyController.setupSocketHandlers(authenticatedSocket);
    bingoController.setupSocketHandlers(authenticatedSocket);
    matchingMayhemController.setupSocketHandlers(authenticatedSocket);
    numberSequenceController.setupSocketHandlers(authenticatedSocket);
    findingLuigiController.setupSocketHandlers(authenticatedSocket);
    numberConnectController.setupSocketHandlers(authenticatedSocket);

    // Handle room joining using new room service
    authenticatedSocket.on('join_room', async (data) => {
      try {
        const { roomId, roomCode, password, gameId } = data;

        // Update user activity
        sessionService.updateUserActivity(user.userId);

        logger.info(`User attempting to join room`, {
          userId: user.userId,
          roomId,
          roomCode,
          gameId: gameId || user.gameId
        });

        // Try to join room using room service
        const joinRequest = {
          roomId,
          roomCode,
          userId: user.userId,
          userName: user.username,
          password,
          role: PlayerRole.Player
        };

        const { room, player } = await roomService.joinRoom(joinRequest);

        // Join the socket room
        authenticatedSocket.join(room.id);

        logger.info(`User successfully joined room via room service`, {
          userId: user.userId,
          roomId: room.id,
          roomCode: room.code,
          role: player.role,
          currentPlayers: room.currentPlayers
        });

        // Handle room switching if user was in a different room
        if (user.roomId && user.roomId !== room.id) {
          const oldRoomId = user.roomId;

          logger.info(`User switching rooms`, {
            userId: user.userId,
            fromRoom: oldRoomId,
            toRoom: room.id
          });

          // Leave old room via room service
          const { room: oldRoom } = roomService.leaveRoom(oldRoomId, user.userId);

          // Notify old room that user is leaving
          authenticatedSocket.to(oldRoomId).emit('player_left', {
            userId: user.userId,
            name: user.username,
            reason: 'joined_other_room'
          });

          // Update old room's player list if room still exists
          if (oldRoom) {
            const oldRoomPlayers = oldRoom.players.map(p => ({
              userId: p.userId,
              name: p.name,
              displayName: p.displayName,
              isHost: p.role === PlayerRole.Host,
              isReady: p.isReady,
              isConnected: p.isConnected,
              joinedAt: p.joinedAt
            }));

            io.to(oldRoomId).emit('room_players_update', {
              players: oldRoomPlayers,
              hostUserId: oldRoom.hostUserId
            });
          }
        }

        // Update session service to track the user in the new room
        sessionService.moveUserToRoom(user.userId, room.id, gameId || user.gameId);

        // Convert room players to client format
        const players = room.players.map(p => ({
          userId: p.userId,
          name: p.name,
          displayName: p.displayName,
          isHost: p.role === PlayerRole.Host,
          isReady: p.isReady,
          isConnected: p.isConnected,
          joinedAt: p.joinedAt
        }));

        // Send room joined confirmation to the joining user
        authenticatedSocket.emit('room_joined', {
          roomId: room.id,
          roomCode: room.code,
          userId: user.userId,
          gameId: gameId || user.gameId,
          players,
          hostUserId: room.hostUserId,
          maxPlayers: room.maxPlayers,
          message: 'Successfully joined room'
        });

        // Notify other players in the room about the new player
        authenticatedSocket.to(room.id).emit('player_joined', {
          userId: user.userId,
          name: user.username,
          displayName: user.username,
          gameId: gameId || user.gameId,
          role: player.role
        });

        // Broadcast updated player list to all users in room
        logger.info(`Broadcasting player list update to room ${room.id}`, {
          playerCount: players.length,
          players: players.map(p => ({ userId: p.userId, name: p.name, isHost: p.isHost })),
          hostUserId: room.hostUserId
        });

        io.to(room.id).emit('room_players_update', {
          players,
          hostUserId: room.hostUserId
        });

      } catch (error) {
        logger.error('Error joining room:', error);
        authenticatedSocket.emit('room_error', {
          message: error instanceof Error ? error.message : 'Failed to join room',
          code: 'JOIN_ROOM_FAILED'
        });
      }
    });

    // Handle room leaving using room service
    authenticatedSocket.on('leave_room', (data) => {
      try {
        const { roomId } = data;

        logger.info(`User leaving room`, {
          userId: user.userId,
          roomId
        });

        // Leave the socket room
        authenticatedSocket.leave(roomId);

        // Leave room via room service
        const { room, wasHost } = roomService.leaveRoom(roomId, user.userId);

        // Send confirmation to leaving user
        authenticatedSocket.emit('room_left', { roomId, userId: user.userId });

        // Notify other players in the room
        authenticatedSocket.to(roomId).emit('player_left', {
          userId: user.userId,
          name: user.username
        });

        // If room still exists, update player list and handle host changes
        if (room) {
          // Convert room players to client format
          const players = room.players.map(p => ({
            userId: p.userId,
            name: p.name,
            displayName: p.displayName,
            isHost: p.role === PlayerRole.Host,
            isReady: p.isReady,
            isConnected: p.isConnected,
            joinedAt: p.joinedAt
          }));

          // If host changed, notify players
          if (wasHost && room.players.length > 0) {
            const newHost = room.players.find(p => p.role === PlayerRole.Host);
            if (newHost) {
              logger.info(`Host transferred from ${user.userId} to ${newHost.userId} in room ${roomId}`);

              io.to(roomId).emit('host_changed', {
                roomId,
                oldHostUserId: user.userId,
                hostUserId: newHost.userId,
                message: `${newHost.name} is now the host`
              });
            }
          }

          // Broadcast updated player list
          io.to(roomId).emit('room_players_update', {
            players,
            hostUserId: room.hostUserId
          });
        }

        // Remove user from session service room tracking
        sessionService.removeUserSession(user.userId);

      } catch (error) {
        logger.error('Error leaving room:', error);
        authenticatedSocket.emit('room_error', {
          message: error instanceof Error ? error.message : 'Failed to leave room',
          code: 'LEAVE_ROOM_FAILED'
        });
      }
    });

    // Handle player ready status using room service
    authenticatedSocket.on('player_ready', (data) => {
      try {
        const { roomId, playerId, ready } = data;

        logger.info(`Player ${playerId} ready status: ${ready} in room ${roomId}`);

        // Update ready status via room service
        const room = roomService.updatePlayerReady(roomId, playerId || user.userId, ready);

        if (room) {
          // Convert room players to client format
          const players = room.players.map(p => ({
            userId: p.userId,
            name: p.name,
            displayName: p.displayName,
            isHost: p.role === PlayerRole.Host,
            isReady: p.isReady,
            isConnected: p.isConnected,
            joinedAt: p.joinedAt
          }));

          // Broadcast updated player list with ready states
          io.to(roomId).emit('room_players_update', {
            players,
            hostUserId: room.hostUserId
          });

          // Also emit specific ready update
          io.to(roomId).emit('player_ready_update', {
            playerId: playerId || user.userId,
            ready,
            allReady: roomService.areAllPlayersReady(roomId)
          });
        }
      } catch (error) {
        logger.error('Error updating player ready status:', error);
        authenticatedSocket.emit('room_error', {
          message: 'Failed to update ready status',
          code: 'READY_UPDATE_FAILED'
        });
      }
    });

    // Handle game actions
    authenticatedSocket.on('game_action', (data) => {
      const { roomId, playerId, action, gameData } = data;
      console.log(`Game action from ${playerId} in room ${roomId}:`, action);

      // Broadcast action to other players in the room
      authenticatedSocket.to(roomId).emit('opponent_action', {
        playerId,
        action,
        gameData,
        timestamp: Date.now()
      });
    });

    // Handle score submission
    authenticatedSocket.on('submit_score', (data) => {
      const { roomId, playerId, score, gameType } = data;
      console.log(`Score submission from ${playerId}: ${score} in room ${roomId}`);

      // TODO: Validate and submit score to Python backend

      // Broadcast score update to room
      authenticatedSocket.to(roomId).emit('score_update', {
        playerId,
        score,
        gameType,
        timestamp: Date.now()
      });
    });

    // Handle game start (host only) using room service
    authenticatedSocket.on('start_game', (data) => {
      try {
        const { roomId, gameType } = data;

        // Get room from room service
        const room = roomService.getRoom(roomId);
        if (!room) {
          authenticatedSocket.emit('room_error', {
            message: 'Room not found',
            code: 'ROOM_NOT_FOUND'
          });
          return;
        }

        // Verify host status
        if (room.hostUserId !== user.userId) {
          authenticatedSocket.emit('room_error', {
            message: 'Only the host can start the game',
            code: 'NOT_HOST'
          });
          return;
        }

        // Check if room allows host-only start
        if (room.config.hostOnlyStart && room.hostUserId !== user.userId) {
          authenticatedSocket.emit('room_error', {
            message: 'Only the host can start the game',
            code: 'HOST_ONLY_START'
          });
          return;
        }

        // Verify minimum players
        if (room.currentPlayers < 2) {
          authenticatedSocket.emit('room_error', {
            message: 'Need at least 2 players to start the game',
            code: 'INSUFFICIENT_PLAYERS'
          });
          return;
        }

        // Check if all players are ready (if required)
        if (!roomService.areAllPlayersReady(roomId)) {
          authenticatedSocket.emit('room_error', {
            message: 'All players must be ready to start the game',
            code: 'PLAYERS_NOT_READY'
          });
          return;
        }

        // Update room status to starting
        roomService.updateRoomStatus(roomId, RoomStatus.Starting);

        logger.info(`Host starting game ${gameType || room.gameType} in room ${roomId}`, {
          hostUserId: user.userId,
          playerCount: room.currentPlayers,
          gameType: gameType || room.gameType
        });

        // Broadcast game start to all players in room
        io.to(roomId).emit('game_start', {
          gameType: gameType || room.gameType,
          roomId,
          startTime: Date.now(),
          hostUserId: user.userId,
          playerCount: room.currentPlayers,
          message: 'Game has started!'
        });

        // Update room status to active after a short delay
        setTimeout(() => {
          roomService.updateRoomStatus(roomId, RoomStatus.Active);
        }, 1000);

      } catch (error) {
        logger.error('Error starting game:', error);
        authenticatedSocket.emit('room_error', {
          message: error instanceof Error ? error.message : 'Failed to start game',
          code: 'START_GAME_FAILED'
        });
      }
    });

    // Handle room creation
    authenticatedSocket.on('create_room', async (data) => {
      try {
        const { gameType, config } = data;

        const createRequest = {
          gameType: gameType || user.gameId,
          hostUserId: user.userId,
          hostName: user.username,
          config: config || {}
        };

        const room = await roomService.createRoom(createRequest);

        // Join the socket room
        authenticatedSocket.join(room.id);

        // Update session service
        sessionService.moveUserToRoom(user.userId, room.id, gameType || user.gameId);

        // Convert room players to client format
        const players = room.players.map(p => ({
          userId: p.userId,
          name: p.name,
          displayName: p.displayName,
          isHost: p.role === PlayerRole.Host,
          isReady: p.isReady,
          isConnected: p.isConnected,
          joinedAt: p.joinedAt
        }));

        // Send room created confirmation
        authenticatedSocket.emit('room_created', {
          roomId: room.id,
          roomCode: room.code,
          userId: user.userId,
          gameId: gameType || user.gameId,
          players,
          hostUserId: room.hostUserId,
          maxPlayers: room.maxPlayers,
          message: 'Room created successfully'
        });

        logger.info('Room created via socket', {
          roomId: room.id,
          roomCode: room.code,
          hostUserId: user.userId,
          gameType: room.gameType
        });

      } catch (error) {
        logger.error('Error creating room:', error);
        authenticatedSocket.emit('room_error', {
          message: error instanceof Error ? error.message : 'Failed to create room',
          code: 'CREATE_ROOM_FAILED'
        });
      }
    });

    // Handle join room by code
    authenticatedSocket.on('join_room_by_code', async (data) => {
      try {
        const { roomCode } = data;

        const joinRequest = {
          roomCode,
          userId: user.userId,
          userName: user.username,
          role: PlayerRole.Player
        };

        const { room, player } = await roomService.joinRoom(joinRequest);

        // Join the socket room
        authenticatedSocket.join(room.id);

        // Handle room switching if needed
        if (user.roomId && user.roomId !== room.id) {
          roomService.leaveRoom(user.roomId, user.userId);
          authenticatedSocket.to(user.roomId).emit('player_left', {
            userId: user.userId,
            name: user.username,
            reason: 'joined_other_room'
          });
        }

        // Update session service
        sessionService.moveUserToRoom(user.userId, room.id, user.gameId);

        // Convert room players to client format
        const players = room.players.map(p => ({
          userId: p.userId,
          name: p.name,
          displayName: p.displayName,
          isHost: p.role === PlayerRole.Host,
          isReady: p.isReady,
          isConnected: p.isConnected,
          joinedAt: p.joinedAt
        }));

        // Send room joined confirmation
        authenticatedSocket.emit('room_joined', {
          roomId: room.id,
          roomCode: room.code,
          userId: user.userId,
          gameId: user.gameId,
          players,
          hostUserId: room.hostUserId,
          maxPlayers: room.maxPlayers,
          message: 'Successfully joined room'
        });

        // Notify other players
        authenticatedSocket.to(room.id).emit('player_joined', {
          userId: user.userId,
          name: user.username,
          displayName: user.username,
          role: player.role
        });

        // Broadcast updated player list
        io.to(room.id).emit('room_players_update', {
          players,
          hostUserId: room.hostUserId
        });

      } catch (error) {
        logger.error('Error joining room by code:', error);
        authenticatedSocket.emit('room_error', {
          message: error instanceof Error ? error.message : 'Failed to join room',
          code: 'JOIN_BY_CODE_FAILED'
        });
      }
    });

    // Handle game end
    authenticatedSocket.on('end_game', (data) => {
      try {
        const { roomId, gameType, results } = data;

        logger.info(`Ending game ${gameType} in room ${roomId}`);

        // Update room status to ended
        roomService.updateRoomStatus(roomId, RoomStatus.Ended);

        // Broadcast game end to all players in room
        io.to(roomId).emit('game_end', {
          gameType,
          results,
          endTime: Date.now(),
          message: 'Game has ended!'
        });
      } catch (error) {
        logger.error('Error ending game:', error);
      }
    });

    // Handle disconnection using room service
    authenticatedSocket.on('disconnect', () => {
      logger.info(`Authenticated client disconnected`, {
        socketId: authenticatedSocket.id,
        userId: user.userId,
        roomId: user.roomId
      });

      // Get all rooms this socket was in
      const rooms = Array.from(authenticatedSocket.rooms);

      // Handle room cleanup and host transfer for each room using room service
      rooms.forEach(roomId => {
        if (roomId !== authenticatedSocket.id) {
          try {
            // Leave room via room service
            const { room, wasHost } = roomService.leaveRoom(roomId, user.userId);

            logger.info(`Cleaning up room due to disconnect`, {
              roomId,
              userId: user.userId,
              wasHost,
              roomExists: !!room
            });

            // Notify other players about disconnection
            authenticatedSocket.to(roomId).emit('player_disconnected', {
              userId: user.userId,
              socketId: authenticatedSocket.id,
              timestamp: Date.now()
            });

            // If room still exists, handle updates
            if (room) {
              // Convert room players to client format
              const players = room.players.map(p => ({
                userId: p.userId,
                name: p.name,
                displayName: p.displayName,
                isHost: p.role === PlayerRole.Host,
                isReady: p.isReady,
                isConnected: p.isConnected,
                joinedAt: p.joinedAt
              }));

              // If host changed due to disconnect, notify players
              if (wasHost && room.players.length > 0) {
                const newHost = room.players.find(p => p.role === PlayerRole.Host);
                if (newHost) {
                  logger.info(`Host transferred from ${user.userId} to ${newHost.userId} in room ${roomId} due to disconnect`);

                  io.to(roomId).emit('host_changed', {
                    roomId,
                    oldHostUserId: user.userId,
                    hostUserId: newHost.userId,
                    reason: 'host_disconnected',
                    message: `${newHost.name} is now the host (previous host disconnected)`
                  });
                }
              }

              // Broadcast updated player list
              io.to(roomId).emit('room_players_update', {
                players,
                hostUserId: room.hostUserId
              });
            }

            // Trigger cleanup in all game controllers for the disconnecting user
            fingerFrenzyController.cleanup(roomId, user.userId);
            bingoController.cleanup(roomId, user.userId);
            matchingMayhemController.cleanup(roomId, user.userId);
            numberSequenceController.cleanup(roomId, user.userId);
            findingLuigiController.cleanup(roomId, user.userId);

          } catch (error) {
            logger.error('Error during disconnect cleanup:', error);
          }
        }
      });

      // Clean up user session after room processing
      sessionService.removeUserSessionBySocket(authenticatedSocket.id);
    });

    // Basic message echo for testing
    authenticatedSocket.on('message', (data) => {
      console.log(`Received message: ${data}`);
      authenticatedSocket.emit('message', `Echo: ${data}`);
    });
  });
}
