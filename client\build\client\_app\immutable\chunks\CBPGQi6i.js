var Ft=Array.isArray,qt=Array.prototype.indexOf,Pn=Array.from,We=Object.defineProperty,ie=Object.getOwnPropertyDescriptor,Lt=Object.getOwnPropertyDescriptors,jt=Object.prototype,Yt=Array.prototype,st=Object.getPrototypeOf,Xe=Object.isExtensible;const Ht=()=>{};function Mn(e){return e()}function at(e){for(var t=0;t<e.length;t++)e[t]()}function Ut(){var e,t,n=new Promise((r,a)=>{e=r,t=a});return{promise:n,resolve:e,reject:t}}const A=2,Ne=4,Ee=8,ce=16,F=32,ne=64,it=128,C=256,he=512,m=1024,x=2048,W=4096,Y=8192,re=16384,De=32768,Ie=65536,Ze=1<<17,Bt=1<<18,Pe=1<<19,Me=1<<20,Re=1<<21,Fe=1<<22,B=1<<23,V=Symbol("$state"),Fn=Symbol("legacy props"),qn=Symbol(""),qe=new class extends Error{name="StaleReactionError";message="The reaction that called `getAbortSignal()` was re-run or destroyed"},Le=3,ft=8;function Vt(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function $t(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function Kt(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function Gt(e){throw new Error("https://svelte.dev/e/effect_in_teardown")}function Wt(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function Xt(e){throw new Error("https://svelte.dev/e/effect_orphan")}function Zt(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function jn(){throw new Error("https://svelte.dev/e/get_abort_signal_outside_reaction")}function Yn(){throw new Error("https://svelte.dev/e/hydration_failed")}function Hn(e){throw new Error("https://svelte.dev/e/lifecycle_legacy_only")}function Un(e){throw new Error("https://svelte.dev/e/props_invalid_value")}function zt(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function Jt(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Qt(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}const Bn=1,Vn=2,$n=4,Kn=8,Gn=16,Wn=1,Xn=2,Zn=4,zn=8,Jn=16,ut=1,en=2,tn="[",nn="[!",rn="]",je={},E=Symbol(),Qn="http://www.w3.org/1999/xhtml",er="@attach";function Ye(e){console.warn("https://svelte.dev/e/hydration_mismatch")}function tr(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let b=!1;function nr(e){b=e}let p;function J(e){if(e===null)throw Ye(),je;return p=e}function lt(){return J(X(p))}function rr(e){if(b){if(X(p)!==null)throw Ye(),je;p=e}}function sr(e=1){if(b){for(var t=e,n=p;t--;)n=X(n);p=n}}function ar(){for(var e=0,t=p;;){if(t.nodeType===ft){var n=t.data;if(n===rn){if(e===0)return t;e-=1}else(n===tn||n===nn)&&(e+=1)}var r=X(t);t.remove(),t=r}}function ir(e){if(!e||e.nodeType!==ft)throw Ye(),je;return e.data}function ot(e){return e===this.v}function sn(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function ct(e){return!sn(e,this.v)}let ge=!1;function fr(){ge=!0}let w=null;function de(e){w=e}function ur(e){return be().get(e)}function lr(e,t){return be().set(e,t),t}function or(e){return be().has(e)}function cr(){return be()}function _r(e,t=!1,n){w={p:w,c:null,e:null,s:e,x:null,l:ge&&!t?{s:null,u:null,$:[]}:null}}function vr(e){var t=w,n=t.e;if(n!==null){t.e=null;for(var r of n)xt(r)}return e!==void 0&&(t.x=e),w=t.p,e??{}}function _e(){return!ge||w!==null&&w.l===null}function be(e){return w===null&&$t(),w.c??=new Map(an(w)||void 0)}function an(e){let t=e.p;for(;t!==null;){const n=t.c;if(n!==null)return n;t=t.p}return null}const fn=new WeakMap;function un(e){var t=v;if(t===null)return _.f|=B,e;if((t.f&De)===0){if((t.f&it)===0)throw!t.parent&&e instanceof Error&&_t(e),e;t.b.error(e)}else He(e,t)}function He(e,t){for(;t!==null;){if((t.f&it)!==0)try{t.b.error(e);return}catch(n){e=n}t=t.parent}throw e instanceof Error&&_t(e),e}function _t(e){const t=fn.get(e);t&&(We(e,"message",{value:t.message}),We(e,"stack",{value:t.stack}))}const ln=typeof requestIdleCallback>"u"?e=>setTimeout(e,1):requestIdleCallback;let ue=[],le=[];function vt(){var e=ue;ue=[],at(e)}function ht(){var e=le;le=[],at(e)}function hr(e){ue.length===0&&queueMicrotask(vt),ue.push(e)}function dr(e){le.length===0&&ln(ht),le.push(e)}function on(){ue.length>0&&vt(),le.length>0&&ht()}function cn(){for(var e=v.b;e!==null&&!e.has_pending_snippet();)e=e.parent;return e===null&&Vt(),e}function Ue(e){var t=A|x,n=_!==null&&(_.f&A)!==0?_:null;return v===null||n!==null&&(n.f&C)!==0?t|=C:v.f|=Pe,{ctx:w,deps:null,effects:null,equals:ot,f:t,fn:e,reactions:null,rv:0,v:E,wv:0,parent:n??v,ac:null}}function _n(e,t){let n=v;n===null&&Kt();var r=n.b,a=void 0,s=Ve(E),i=null,l=!_;return bn(()=>{try{var f=e()}catch(h){f=Promise.reject(h)}var u=()=>f;a=i?.then(u,u)??Promise.resolve(f),i=a;var o=R,c=r.pending;l&&(r.update_pending_count(1),c||o.increment());const d=(h,k=void 0)=>{i=null,c||o.activate(),k?k!==qe&&(s.f|=B,Ce(s,k)):((s.f&B)!==0&&(s.f^=B),Ce(s,h)),l&&(r.update_pending_count(-1),c||o.decrement()),wt()};if(a.then(d,h=>d(null,h||"unknown")),o)return()=>{queueMicrotask(()=>o.neuter())}}),new Promise(f=>{function u(o){function c(){o===a?f(s):u(a)}o.then(c,c)}u(a)})}function pr(e){const t=Ue(e);return Ot(t),t}function vn(e){const t=Ue(e);return t.equals=ct,t}function dt(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)H(t[n])}}function hn(e){for(var t=e.parent;t!==null;){if((t.f&A)===0)return t;t=t.parent}return null}function Be(e){var t,n=v;we(hn(e));try{dt(e),t=It(e)}finally{we(n)}return t}function pt(e){var t=Be(e);if(e.equals(t)||(e.v=t,e.wv=Nt()),!se)if(Q!==null)Q.set(e,e.v);else{var n=(L||(e.f&C)!==0)&&e.deps!==null?W:m;y(e,n)}}function dn(e,t,n){const r=_e()?Ue:vn;if(t.length===0){n(e.map(r));return}var a=R,s=v,i=pn(),l=cn();Promise.all(t.map(f=>_n(f))).then(f=>{a?.activate(),i();try{n([...e.map(r),...f])}catch(u){(s.f&re)===0&&He(u,s)}a?.deactivate(),wt()}).catch(f=>{l.error(f)})}function pn(){var e=v,t=_,n=w;return function(){we(e),te(t),de(n)}}function wt(){we(null),te(null),de(null)}const ve=new Set;let R=null,Q=null,ze=new Set,pe=[];function mt(){const e=pe.shift();pe.length>0&&queueMicrotask(mt),e()}let Z=[],Te=null,Se=!1;class G{#u=new Map;#a=new Map;#i=new Set;#t=0;#l=null;#o=!1;#r=[];#f=[];#n=[];#e=[];#s=[];skipped_effects=new Set;#_(t){Z=[];var n=null;if(ve.size>1){n=new Map,Q=new Map;for(const[s,i]of this.#u)n.set(s,{v:s.v,wv:s.wv}),s.v=i;for(const s of ve)if(s!==this)for(const[i,l]of s.#a)n.has(i)||(n.set(i,{v:i.v,wv:i.wv}),i.v=l)}for(const s of t)this.#v(s);if(this.#r.length===0&&this.#t===0){var r=this.#n,a=this.#e;this.#n=[],this.#e=[],this.#s=[],this.#c(),Je(r),Je(a),this.#l?.resolve()}else{for(const s of this.#n)y(s,m);for(const s of this.#e)y(s,m);for(const s of this.#s)y(s,m)}if(n){for(const[s,{v:i,wv:l}]of n)s.wv<=l&&(s.v=i);Q=null}for(const s of this.#r)fe(s);for(const s of this.#f)fe(s);this.#r=[],this.#f=[]}#v(t){t.f^=m;for(var n=t.first;n!==null;){var r=n.f,a=(r&(F|ne))!==0,s=a&&(r&m)!==0,i=s||(r&Y)!==0||this.skipped_effects.has(n);if(!i&&n.fn!==null){if(a)n.f^=m;else if((r&Ne)!==0)this.#e.push(n);else if(xe(n))if((r&Fe)!==0){var l=n.b?.pending?this.#f:this.#r;l.push(n)}else(n.f&ce)!==0&&this.#s.push(n),fe(n);var f=n.first;if(f!==null){n=f;continue}}var u=n.parent;for(n=n.next;n===null&&u!==null;)n=u.next,u=u.parent}}capture(t,n){this.#a.has(t)||this.#a.set(t,n),this.#u.set(t,t.v)}activate(){R=this}deactivate(){R=null;for(const t of ze)if(ze.delete(t),t(),R!==null)break}neuter(){this.#o=!0}flush(){Z.length>0?this.flush_effects():this.#c(),R===this&&(this.#t===0&&ve.delete(this),this.deactivate())}flush_effects(){var t=z;Se=!0;try{var n=0;for(tt(!0);Z.length>0;){if(n++>1e3){var r,a;mn()}this.#_(Z),$.clear()}}finally{Se=!1,tt(t),Te=null}}#c(){if(!this.#o)for(const t of this.#i)t();this.#i.clear()}increment(){this.#t+=1}decrement(){if(this.#t-=1,this.#t===0){for(const t of this.#n)y(t,x),j(t);for(const t of this.#e)y(t,x),j(t);for(const t of this.#s)y(t,x),j(t);this.#n=[],this.#e=[],this.flush()}else this.deactivate()}add_callback(t){this.#i.add(t)}settled(){return(this.#l??=Ut()).promise}static ensure(t=!0){if(R===null){const n=R=new G;ve.add(R),t&&G.enqueue(()=>{R===n&&n.flush()})}return R}static enqueue(t){pe.length===0&&queueMicrotask(mt),pe.unshift(t)}}function wn(e){var t;const n=G.ensure(!1);for(e&&(n.flush_effects(),t=e());;){if(on(),Z.length===0)return n===R&&n.flush(),Te=null,t;n.flush_effects()}}function mn(){try{Zt()}catch(e){He(e,Te)}}function Je(e){var t=e.length;if(t!==0){for(var n=0;n<t;n++){var r=e[n];if((r.f&(re|Y))===0&&xe(r)){var a=me;if(fe(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null&&r.ac===null?Rt(r):r.fn=null),me>a&&(r.f&Me)!==0)break}}for(;n<t;n+=1)j(e[n])}}function j(e){for(var t=Te=e;t.parent!==null;){t=t.parent;var n=t.f;if(Se&&t===v&&(n&ce)!==0)return;if((n&(ne|F))!==0){if((n&m)===0)return;t.f^=m}}Z.push(t)}const $=new Map;function Ve(e,t){var n={f:0,v:e,reactions:null,equals:ot,rv:0,wv:0};return n}function q(e,t){const n=Ve(e);return Ot(n),n}function wr(e,t=!1,n=!0){const r=Ve(e);return t||(r.equals=ct),ge&&n&&w!==null&&w.l!==null&&(w.l.s??=[]).push(r),r}function mr(e,t){return P(e,Nn(()=>U(e))),t}function P(e,t,n=!1){_!==null&&(!D||(_.f&Ze)!==0)&&_e()&&(_.f&(A|ce|Fe|Ze))!==0&&!M?.includes(e)&&Qt();let r=n?ae(t):t;return Ce(e,r)}function Ce(e,t){if(!e.equals(t)){var n=e.v;se?$.set(e,t):$.set(e,n),e.v=t,G.ensure().capture(e,n),(e.f&A)!==0&&((e.f&x)!==0&&Be(e),y(e,(e.f&C)===0?m:W)),e.wv=Nt(),yt(e,x),_e()&&v!==null&&(v.f&m)!==0&&(v.f&(F|ne))===0&&(S===null?Cn([e]):S.push(e))}return t}function yr(e,t=1){var n=U(e),r=t===1?n++:n--;return P(e,n),r}function ke(e){P(e,e.v+1)}function yt(e,t){var n=e.reactions;if(n!==null)for(var r=_e(),a=n.length,s=0;s<a;s++){var i=n[s],l=i.f;!r&&i===v||((l&x)===0&&y(i,t),(l&A)!==0?yt(i,W):(l&x)===0&&j(i))}}function ae(e){if(typeof e!="object"||e===null||V in e)return e;const t=st(e);if(t!==jt&&t!==Yt)return e;var n=new Map,r=Ft(e),a=q(0),s=K,i=l=>{if(K===s)return l();var f=_,u=K;te(null),rt(s);var o=l();return te(f),rt(u),o};return r&&n.set("length",q(e.length)),new Proxy(e,{defineProperty(l,f,u){(!("value"in u)||u.configurable===!1||u.enumerable===!1||u.writable===!1)&&zt();var o=n.get(f);return o===void 0?o=i(()=>{var c=q(u.value);return n.set(f,c),c}):P(o,u.value,!0),!0},deleteProperty(l,f){var u=n.get(f);if(u===void 0){if(f in l){const o=i(()=>q(E));n.set(f,o),ke(a)}}else P(u,E),ke(a);return!0},get(l,f,u){if(f===V)return e;var o=n.get(f),c=f in l;if(o===void 0&&(!c||ie(l,f)?.writable)&&(o=i(()=>{var h=ae(c?l[f]:E),k=q(h);return k}),n.set(f,o)),o!==void 0){var d=U(o);return d===E?void 0:d}return Reflect.get(l,f,u)},getOwnPropertyDescriptor(l,f){var u=Reflect.getOwnPropertyDescriptor(l,f);if(u&&"value"in u){var o=n.get(f);o&&(u.value=U(o))}else if(u===void 0){var c=n.get(f),d=c?.v;if(c!==void 0&&d!==E)return{enumerable:!0,configurable:!0,value:d,writable:!0}}return u},has(l,f){if(f===V)return!0;var u=n.get(f),o=u!==void 0&&u.v!==E||Reflect.has(l,f);if(u!==void 0||v!==null&&(!o||ie(l,f)?.writable)){u===void 0&&(u=i(()=>{var d=o?ae(l[f]):E,h=q(d);return h}),n.set(f,u));var c=U(u);if(c===E)return!1}return o},set(l,f,u,o){var c=n.get(f),d=f in l;if(r&&f==="length")for(var h=u;h<c.v;h+=1){var k=n.get(h+"");k!==void 0?P(k,E):h in l&&(k=i(()=>q(E)),n.set(h+"",k))}if(c===void 0)(!d||ie(l,f)?.writable)&&(c=i(()=>q(void 0)),P(c,ae(u)),n.set(f,c));else{d=c.v!==E;var Mt=i(()=>ae(u));P(c,Mt)}var Ke=Reflect.getOwnPropertyDescriptor(l,f);if(Ke?.set&&Ke.set.call(o,u),!d){if(r&&typeof f=="string"){var Ge=n.get("length"),Ae=Number(f);Number.isInteger(Ae)&&Ae>=Ge.v&&P(Ge,Ae+1)}ke(a)}return!0},ownKeys(l){U(a);var f=Reflect.ownKeys(l).filter(c=>{var d=n.get(c);return d===void 0||d.v!==E});for(var[u,o]of n)o.v!==E&&!(u in l)&&f.push(u);return f},setPrototypeOf(){Jt()}})}function Qe(e){try{if(e!==null&&typeof e=="object"&&V in e)return e[V]}catch{}return e}function Er(e,t){return Object.is(Qe(e),Qe(t))}var et,yn,Et,gt,bt;function gr(){if(et===void 0){et=window,yn=document,Et=/Firefox/.test(navigator.userAgent);var e=Element.prototype,t=Node.prototype,n=Text.prototype;gt=ie(t,"firstChild").get,bt=ie(t,"nextSibling").get,Xe(e)&&(e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__style=void 0,e.__e=void 0),Xe(n)&&(n.__t=void 0)}}function ee(e=""){return document.createTextNode(e)}function O(e){return gt.call(e)}function X(e){return bt.call(e)}function br(e,t){if(!b)return O(e);var n=O(p);if(n===null)n=p.appendChild(ee());else if(t&&n.nodeType!==Le){var r=ee();return n?.before(r),J(r),r}return J(n),n}function Tr(e,t){if(!b){var n=O(e);return n instanceof Comment&&n.data===""?X(n):n}return p}function xr(e,t=1,n=!1){let r=b?p:e;for(var a;t--;)a=r,r=X(r);if(!b)return r;if(n&&r?.nodeType!==Le){var s=ee();return r===null?a?.after(s):r.before(s),J(s),s}return J(r),r}function Ar(e){e.textContent=""}function kr(){return!1}function Tt(e){v===null&&_===null&&Xt(),_!==null&&(_.f&C)!==0&&v===null&&Wt(),se&&Gt()}function En(e,t){var n=t.last;n===null?t.last=t.first=e:(n.next=e,e.prev=n,t.last=e)}function I(e,t,n,r=!0){var a=v;a!==null&&(a.f&Y)!==0&&(e|=Y);var s={ctx:w,deps:null,nodes_start:null,nodes_end:null,f:e|x,first:null,fn:t,last:null,next:null,parent:a,b:a&&a.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{fe(s),s.f|=De}catch(f){throw H(s),f}else t!==null&&j(s);var i=n&&s.deps===null&&s.first===null&&s.nodes_start===null&&s.teardown===null&&(s.f&Pe)===0;if(!i&&r&&(a!==null&&En(s,a),_!==null&&(_.f&A)!==0)){var l=_;(l.effects??=[]).push(s)}return s}function gn(e){const t=I(Ee,null,!1);return y(t,m),t.teardown=e,t}function Rr(e){Tt();var t=v.f,n=!_&&(t&F)!==0&&(t&De)===0;if(n){var r=w;(r.e??=[]).push(e)}else return xt(e)}function xt(e){return I(Ne|Me,e,!1)}function Sr(e){return Tt(),I(Ee|Me,e,!0)}function Cr(e){G.ensure();const t=I(ne,e,!0);return(n={})=>new Promise(r=>{n.outro?Rn(t,()=>{H(t),r(void 0)}):(H(t),r(void 0))})}function Or(e){return I(Ne,e,!1)}function bn(e){return I(Fe|Pe,e,!0)}function Nr(e,t=0){return I(Ee|t,e,!0)}function Dr(e,t=[],n=[]){dn(t,n,r=>{I(Ee,()=>e(...r.map(U)),!0)})}function Tn(e,t=0){var n=I(ce|t,e,!0);return n}function xn(e,t=!0){return I(F,e,!0,t)}function At(e){var t=e.teardown;if(t!==null){const n=se,r=_;nt(!0),te(null);try{t.call(null)}finally{nt(n),te(r)}}}function kt(e,t=!1){var n=e.first;for(e.first=e.last=null;n!==null;){n.ac?.abort(qe);var r=n.next;(n.f&ne)!==0?n.parent=null:H(n,t),n=r}}function An(e){for(var t=e.first;t!==null;){var n=t.next;(t.f&F)===0&&H(t),t=n}}function H(e,t=!0){var n=!1;(t||(e.f&Bt)!==0)&&e.nodes_start!==null&&e.nodes_end!==null&&(kn(e.nodes_start,e.nodes_end),n=!0),kt(e,t&&!n),ye(e,0),y(e,re);var r=e.transitions;if(r!==null)for(const s of r)s.stop();At(e);var a=e.parent;a!==null&&a.first!==null&&Rt(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=e.ac=null}function kn(e,t){for(;e!==null;){var n=e===t?null:X(e);e.remove(),e=n}}function Rt(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function Rn(e,t){var n=[];St(e,n,!0),Sn(n,()=>{H(e),t&&t()})}function Sn(e,t){var n=e.length;if(n>0){var r=()=>--n||t();for(var a of e)a.out(r)}else t()}function St(e,t,n){if((e.f&Y)===0){if(e.f^=Y,e.transitions!==null)for(const i of e.transitions)(i.is_global||n)&&t.push(i);for(var r=e.first;r!==null;){var a=r.next,s=(r.f&Ie)!==0||(r.f&F)!==0;St(r,t,s?n:!1),r=a}}}function Ir(e){Ct(e,!0)}function Ct(e,t){if((e.f&Y)!==0){e.f^=Y,(e.f&m)===0&&(y(e,x),j(e));for(var n=e.first;n!==null;){var r=n.next,a=(n.f&Ie)!==0||(n.f&F)!==0;Ct(n,a?t:!1),n=r}if(e.transitions!==null)for(const s of e.transitions)(s.is_global||t)&&s.in()}}let z=!1;function tt(e){z=e}let se=!1;function nt(e){se=e}let _=null,D=!1;function te(e){_=e}let v=null;function we(e){v=e}let M=null;function Ot(e){_!==null&&(M===null?M=[e]:M.push(e))}let g=null,T=0,S=null;function Cn(e){S=e}let me=1,oe=0,K=oe;function rt(e){K=e}let L=!1;function Nt(){return++me}function xe(e){var t=e.f;if((t&x)!==0)return!0;if((t&W)!==0){var n=e.deps,r=(t&C)!==0;if(n!==null){var a,s,i=(t&he)!==0,l=r&&v!==null&&!L,f=n.length;if((i||l)&&(v===null||(v.f&re)===0)){var u=e,o=u.parent;for(a=0;a<f;a++)s=n[a],(i||!s?.reactions?.includes(u))&&(s.reactions??=[]).push(u);i&&(u.f^=he),l&&o!==null&&(o.f&C)===0&&(u.f^=C)}for(a=0;a<f;a++)if(s=n[a],xe(s)&&pt(s),s.wv>e.wv)return!0}(!r||v!==null&&!L)&&y(e,m)}return!1}function Dt(e,t,n=!0){var r=e.reactions;if(r!==null&&!M?.includes(e))for(var a=0;a<r.length;a++){var s=r[a];(s.f&A)!==0?Dt(s,t,!1):t===s&&(n?y(s,x):(s.f&m)!==0&&y(s,W),j(s))}}function It(e){var t=g,n=T,r=S,a=_,s=L,i=M,l=w,f=D,u=K,o=e.f;g=null,T=0,S=null,L=(o&C)!==0&&(D||!z||_===null),_=(o&(F|ne))===0?e:null,M=null,de(e.ctx),D=!1,K=++oe,e.ac!==null&&(e.ac.abort(qe),e.ac=null);try{e.f|=Re;var c=(0,e.fn)(),d=e.deps;if(g!==null){var h;if(ye(e,T),d!==null&&T>0)for(d.length=T+g.length,h=0;h<g.length;h++)d[T+h]=g[h];else e.deps=d=g;if(!L||(o&A)!==0&&e.reactions!==null)for(h=T;h<d.length;h++)(d[h].reactions??=[]).push(e)}else d!==null&&T<d.length&&(ye(e,T),d.length=T);if(_e()&&S!==null&&!D&&d!==null&&(e.f&(A|W|x))===0)for(h=0;h<S.length;h++)Dt(S[h],e);return a!==null&&a!==e&&(oe++,S!==null&&(r===null?r=S:r.push(...S))),(e.f&B)!==0&&(e.f^=B),c}catch(k){return un(k)}finally{e.f^=Re,g=t,T=n,S=r,_=a,L=s,M=i,de(l),D=f,K=u}}function On(e,t){let n=t.reactions;if(n!==null){var r=qt.call(n,e);if(r!==-1){var a=n.length-1;a===0?n=t.reactions=null:(n[r]=n[a],n.pop())}}n===null&&(t.f&A)!==0&&(g===null||!g.includes(t))&&(y(t,W),(t.f&(C|he))===0&&(t.f^=he),dt(t),ye(t,0))}function ye(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)On(e,n[r])}function fe(e){var t=e.f;if((t&re)===0){y(e,m);var n=v,r=z;v=e,z=!0;try{(t&ce)!==0?An(e):kt(e),At(e);var a=It(e);e.teardown=typeof a=="function"?a:null,e.wv=me;var s}finally{z=r,v=n}}}async function Pr(){await Promise.resolve(),wn()}function Mr(){return G.ensure().settled()}function U(e){var t=e.f,n=(t&A)!==0;if(_!==null&&!D){var r=v!==null&&(v.f&re)!==0;if(!r&&!M?.includes(e)){var a=_.deps;if((_.f&Re)!==0)e.rv<oe&&(e.rv=oe,g===null&&a!==null&&a[T]===e?T++:g===null?g=[e]:(!L||!g.includes(e))&&g.push(e));else{(_.deps??=[]).push(e);var s=e.reactions;s===null?e.reactions=[_]:s.includes(_)||s.push(_)}}}else if(n&&e.deps===null&&e.effects===null){var i=e,l=i.parent;l!==null&&(l.f&C)===0&&(i.f^=C)}if(se){if($.has(e))return $.get(e);if(n){i=e;var f=i.v;return((i.f&m)===0&&i.reactions!==null||Pt(i))&&(f=Be(i)),$.set(i,f),f}}else if(n){if(i=e,Q?.has(i))return Q.get(i);xe(i)&&pt(i)}if((e.f&B)!==0)throw e.v;return e.v}function Pt(e){if(e.v===E)return!0;if(e.deps===null)return!1;for(const t of e.deps)if($.has(t)||(t.f&A)!==0&&Pt(t))return!0;return!1}function Nn(e){var t=D;try{return D=!0,e()}finally{D=t}}const Dn=-7169;function y(e,t){e.f=e.f&Dn|t}function Fr(e){if(!(typeof e!="object"||!e||e instanceof EventTarget)){if(V in e)Oe(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&V in n&&Oe(n)}}}function Oe(e,t=new Set){if(typeof e=="object"&&e!==null&&!(e instanceof EventTarget)&&!t.has(e)){t.add(e),e instanceof Date&&e.getTime();for(let r in e)try{Oe(e[r],t)}catch{}const n=st(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=Lt(n);for(let a in r){const s=r[a].get;if(s)try{s.call(e)}catch{}}}}}function $e(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function N(e,t){var n=v;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function qr(e,t){var n=(t&ut)!==0,r=(t&en)!==0,a,s=!e.startsWith("<!>");return()=>{if(b)return N(p,null),p;a===void 0&&(a=$e(s?e:"<!>"+e),n||(a=O(a)));var i=r||Et?document.importNode(a,!0):a.cloneNode(!0);if(n){var l=O(i),f=i.lastChild;N(l,f)}else N(i,i);return i}}function In(e,t,n="svg"){var r=!e.startsWith("<!>"),a=(t&ut)!==0,s=`<${n}>${r?e:"<!>"+e}</${n}>`,i;return()=>{if(b)return N(p,null),p;if(!i){var l=$e(s),f=O(l);if(a)for(i=document.createDocumentFragment();O(f);)i.appendChild(O(f));else i=O(f)}var u=i.cloneNode(!0);if(a){var o=O(u),c=u.lastChild;N(o,c)}else N(u,u);return u}}function Lr(e,t){return In(e,t,"svg")}function jr(e=""){if(!b){var t=ee(e+"");return N(t,t),t}var n=p;return n.nodeType!==Le&&(n.before(n=ee()),J(n)),N(n,n),n}function Yr(){if(b)return N(p,null),p;var e=document.createDocumentFragment(),t=document.createComment(""),n=ee();return e.append(t,n),N(t,n),e}function Hr(e,t){if(b){v.nodes_end=p,lt();return}e!==null&&e.before(t)}function Ur(e,t,...n){var r=e,a=Ht,s;Tn(()=>{a!==(a=t())&&(s&&(H(s),s=null),s=xn(()=>a(r,...n)))},Ie),b&&(r=p)}function Br(e){return(t,...n)=>{var r=e(...n),a;if(b)a=p,lt();else{var s=r.render().trim(),i=$e(s);a=O(i),t.before(a)}const l=r.setup?.(a);N(a,a),typeof l=="function"&&gn(l)}}export{yn as $,ar as A,J as B,nr as C,ee as D,Ie as E,xn as F,R as G,nn as H,kr as I,Ir as J,Rn as K,p as L,Or as M,Nr as N,hr as O,gn as P,We as Q,Ht as R,V as S,wr as T,E as U,P as V,ie as W,Un as X,Zn as Y,vn as Z,ae as _,Tr as a,qn as a$,v as a0,re as a1,zn as a2,ge as a3,Xn as a4,Wn as a5,Jn as a6,se as a7,Fn as a8,wn as a9,Br as aA,cr as aB,ur as aC,or as aD,lr as aE,Mr as aF,kn as aG,$e as aH,yr as aI,Lr as aJ,mr as aK,Ce as aL,Ve as aM,Vn as aN,Bn as aO,Gn as aP,Y as aQ,H as aR,St as aS,Sn as aT,$n as aU,Kn as aV,tr as aW,Er as aX,Qn as aY,st as aZ,dr as a_,q as aa,Pr as ab,pr as ac,jr as ad,te as ae,we as af,_ as ag,Ft as ah,Bt as ai,ft as aj,tn as ak,X as al,O as am,gr as an,je as ao,rn as ap,Ye as aq,Yn as ar,Ar as as,Pn as at,Cr as au,N as av,sn as aw,$t as ax,jn as ay,Hn as az,Hr as b,Lt as b0,dn as b1,er as b2,_e as b3,vr as c,br as d,w as e,qr as f,U as g,Rr as h,Nn as i,Mn as j,at as k,Fr as l,Ue as m,sr as n,fr as o,_r as p,Yr as q,rr as r,xr as s,Dr as t,Sr as u,Ur as v,Tn as w,b as x,lt as y,ir as z};
