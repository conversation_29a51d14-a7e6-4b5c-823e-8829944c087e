const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-B_r7d1Ms.js')).default;
const imports = ["_app/immutable/nodes/1.CCDFjfsy.js","_app/immutable/chunks/DsnmJJEf.js","_app/immutable/chunks/Dh9ZwQ9u.js","_app/immutable/chunks/CBPGQi6i.js","_app/immutable/chunks/D7HYObST.js","_app/immutable/chunks/XXcWJRIr.js","_app/immutable/chunks/CbN9a2Ga.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-DRHOCvvO.js.map
