import * as Phaser from 'phaser';
import { gameActions } from '$lib/stores';

export default class GameStartScene extends Phaser.Scene {
  private startButton!: Phaser.GameObjects.Text;
  private titleText!: Phaser.GameObjects.Text;
  private instructionsText!: Phaser.GameObjects.Text;

  constructor() {
    super('GameStartScene');
  }

  create(): void {
    const { width, height } = this.cameras.main;

    // Add background
    this.add.rectangle(width / 2, height / 2, width, height, 0x1E1E1E);

    // Title
    this.titleText = this.add.text(width / 2, height * 0.2, 'Finding Luigi', {
      fontSize: '48px',
      color: '#FFD700',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Instructions
    this.instructionsText = this.add.text(width / 2, height * 0.4, 
      'Find the target faces as quickly as possible!\n\n' +
      '• Look for the faces shown at the top\n' +
      '• Tap on them when you find them\n' +
      '• Be careful - wrong taps cost lives!\n' +
      '• Complete all targets to advance', {
      fontSize: '20px',
      color: '#FFFFFF',
      fontFamily: 'Arial, sans-serif',
      align: 'center',
      lineSpacing: 10
    }).setOrigin(0.5);

    // Start button
    this.startButton = this.add.text(width / 2, height * 0.7, 'TAP TO START', {
      fontSize: '32px',
      color: '#00FF00',
      fontFamily: 'Arial, sans-serif',
      fontStyle: 'bold'
    }).setOrigin(0.5);

    // Make start button interactive
    this.startButton.setInteractive({ useHandCursor: true });
    
    this.startButton.on('pointerdown', () => {
      this.startGame();
    });

    this.startButton.on('pointerover', () => {
      this.startButton.setScale(1.1);
      this.startButton.setColor('#00FFFF');
    });

    this.startButton.on('pointerout', () => {
      this.startButton.setScale(1.0);
      this.startButton.setColor('#00FF00');
    });

    // Add some visual flair
    this.createFloatingFaces();
  }

  private createFloatingFaces(): void {
    const { width, height } = this.cameras.main;
    
    // Create some floating face sprites for decoration
    for (let i = 0; i < 5; i++) {
      const x = Phaser.Math.Between(50, width - 50);
      const y = Phaser.Math.Between(100, height - 100);
      
      // Create a simple colored circle as placeholder
      const face = this.add.circle(x, y, 20, 0x4CAF50, 0.3);
      
      // Add floating animation
      this.tweens.add({
        targets: face,
        y: y - 20,
        duration: 2000 + Math.random() * 1000,
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      });

      this.tweens.add({
        targets: face,
        alpha: 0.1,
        duration: 1500 + Math.random() * 1000,
        yoyo: true,
        repeat: -1,
        ease: 'Sine.easeInOut'
      });
    }
  }

  private startGame(): void {
    // Trigger game initialization
    gameActions.initGame();
    
    // Transition to main game scene
    this.scene.start('GameScene');
  }
}
