import { AuthenticatedUser, UserSession, RoomSession } from '../types/game.js';
import { logger } from '../utils/logger.js';
import { generateFloats } from '../utils/prngService.js';
import { randomBytes } from 'crypto';

/**
 * Session Service for managing authenticated user sessions and room data
 * Integrates with existing game session storage
 */
import type { Server } from 'socket.io';

export class SessionService {
  // Map of userId -> UserSession
  private userSessions: Map<string, UserSession> = new Map();
  // Map of roomId -> RoomSession
  private roomSessions: Map<string, RoomSession> = new Map();
  // Map of socketId -> userId
  private socketToUser: Map<string, string> = new Map();
  // Socket.IO server reference for room broadcasts
  private io: Server | null = null;

  /**
   * Create or update a user session
   * @param user - Authenticated user data
   * @param socketId - Socket ID
   * @returns Created user session
   */
  public createUserSession(user: AuthenticatedUser, socketId: string): UserSession {
    const now = new Date();

    const session: UserSession = {
      user,
      socketId,
      gameState: null,
      connectedAt: now,
      lastActivity: now,
      prngCursor: 0,
    };

    // Store user session
    this.userSessions.set(user.userId, session);
    this.socketToUser.set(socketId, user.userId);

    // Create or update room session
    this.addUserToRoom(user, session);

    logger.info('User session created', {
      userId: user.userId,
      socketId,
      roomId: user.roomId,
      gameId: user.gameId
    });

    return session;
  }

  /**
   * Get user session by user ID
   * @param userId - User ID
   * @returns User session or null
   */
  public getUserSession(userId: string): UserSession | null {
    return this.userSessions.get(userId) || null;
  }

  /**
   * Get user session by socket ID
   * @param socketId - Socket ID
   * @returns User session or null
   */
  public getUserSessionBySocket(socketId: string): UserSession | null {
    const userId = this.socketToUser.get(socketId);
    return userId ? this.getUserSession(userId) : null;
  }

  /**
   * Update user activity timestamp
   * @param userId - User ID
   */
  public updateUserActivity(userId: string): void {
    const session = this.userSessions.get(userId);
    if (session) {
      session.lastActivity = new Date();

      // Also update room activity
      const roomSession = this.roomSessions.get(session.user.roomId);
      if (roomSession) {
        roomSession.lastActivity = new Date();
      }
    }
  }

  /**
   * Remove user session
   * @param userId - User ID
   */
  public removeUserSession(userId: string): void {
    const session = this.userSessions.get(userId);
    if (session) {
      // Remove from socket mapping
      this.socketToUser.delete(session.socketId);

      // Remove from room
      this.removeUserFromRoom(session.user.roomId, userId);

      // Remove user session
      this.userSessions.delete(userId);

      logger.info('User session removed', {
        userId,
        socketId: session.socketId,
        roomId: session.user.roomId
      });
    }
  }

  /**
   * Remove user session by socket ID
   * @param socketId - Socket ID
   */
  public removeUserSessionBySocket(socketId: string): void {
    const userId = this.socketToUser.get(socketId);
    if (userId) {
      this.removeUserSession(userId);
    }
  }

  /**
   * Get room session
   * @param roomId - Room ID
   * @returns Room session or null
   */
  public getRoomSession(roomId: string): RoomSession | null {
    return this.roomSessions.get(roomId) || null;
  }

  /**
   * Get all users in a room
   * @param roomId - Room ID
   * @returns Array of user sessions in the room
   */
  public getRoomUsers(roomId: string): UserSession[] {
    const roomSession = this.roomSessions.get(roomId);
    return roomSession ? Array.from(roomSession.users.values()) : [];
  }

  /**
   * Get user count in a room
   * @param roomId - Room ID
   * @returns Number of users in the room
   */
  public getRoomUserCount(roomId: string): number {
    const roomSession = this.roomSessions.get(roomId);
    return roomSession ? roomSession.users.size : 0;
  }

  /**
   * Check if user is in a specific room
   * @param userId - User ID
   * @param roomId - Room ID
   * @returns true if user is in the room
   */
  public isUserInRoom(userId: string, roomId: string): boolean {
    const roomSession = this.roomSessions.get(roomId);
    return roomSession ? roomSession.users.has(userId) : false;
  }

  /**
   * Get session data for game controllers (compatible with existing structure)
   * @param roomId - Room ID
   * @returns Session data object
   */
  public getGameSessionData(roomId: string): { submitScoreId?: string; authToken?: string } | null {
    const roomSession = this.roomSessions.get(roomId);
    if (!roomSession || roomSession.users.size === 0) {
      return null;
    }

    // Get the first user's data (for single-player games)
    const firstUser = Array.from(roomSession.users.values())[0];
    return {
      submitScoreId: firstUser.user.scoreSubmitId,
      authToken: firstUser.user.authToken
    };
  }
  /**
   * Get PRNG parameters for a room/user
   * If userId is not provided, picks the first user in the room
   */
  public getPrngInfo(roomId: string, userId?: string): { serverSeed: string; clientSeed: string; nonce: number; cursor: number } | null {
    const roomSession = this.roomSessions.get(roomId);
    if (!roomSession) return null;

    const targetUserSession = userId
      ? roomSession.users.get(userId) || null
      : Array.from(roomSession.users.values())[0] || null;

    if (!targetUserSession) return null;

    const serverSeed = roomSession.serverSeed || randomBytes(32).toString('hex');
    if (!roomSession.serverSeed) {
      roomSession.serverSeed = serverSeed;
      roomSession.nonce = 0;
    }

    const clientSeed = targetUserSession.user.clientSeed || `client-${roomId}-${targetUserSession.user.userId}`;
    const nonce = roomSession.nonce ?? 0;
    const cursor = targetUserSession.prngCursor ?? 0;

    return { serverSeed, clientSeed, nonce, cursor };
  }

  /**
   * Generate deterministic floats [0,1) and advance the per-user cursor
   */
  public getPrngFloats(roomId: string, count: number, userId?: string): number[] {
    const info = this.getPrngInfo(roomId, userId);
    if (!info) return [];

    const { serverSeed, clientSeed, nonce, cursor } = info;
    const floats = generateFloats(serverSeed, clientSeed, nonce, cursor, count);

    // Each float consumes 4 bytes in the underlying generator; advance cursor accordingly
    const roomSession = this.roomSessions.get(roomId)!;
    const targetUserSession = userId
      ? roomSession.users.get(userId) || null
      : Array.from(roomSession.users.values())[0] || null;
    if (targetUserSession) {
      targetUserSession.prngCursor = (targetUserSession.prngCursor ?? 0) + (count * 4);
    }

    return floats;
  }

  /**
   * Convenience: get a random integer in [0, max)
   */
  public getPrngInt(roomId: string, max: number, userId?: string): number {
    const [f] = this.getPrngFloats(roomId, 1, userId);
    if (max <= 0 || f === undefined) return 0;
    return Math.floor(f * max);
  }


  /**
   * Clean up expired sessions
   * @param maxIdleTime - Maximum idle time in milliseconds (default: 1 hour)
   */
  public cleanupExpiredSessions(maxIdleTime: number = 60 * 60 * 1000): void {
    const now = new Date();
    const expiredUsers: string[] = [];

    for (const [userId, session] of this.userSessions) {
      if (now.getTime() - session.lastActivity.getTime() > maxIdleTime) {
        expiredUsers.push(userId);
      }
    }

    for (const userId of expiredUsers) {
      this.removeUserSession(userId);
    }

    if (expiredUsers.length > 0) {
      logger.info('Cleaned up expired sessions', { count: expiredUsers.length });
    }
  }

  /**
   * Add user to room session
   * @param user - Authenticated user
   * @param session - User session
   */
  private addUserToRoom(user: AuthenticatedUser, session: UserSession): void {
    let roomSession = this.roomSessions.get(user.roomId);

    if (!roomSession) {
      roomSession = {
        roomId: user.roomId,
        gameId: user.gameId,
        users: new Map(),
        createdAt: new Date(),
        lastActivity: new Date(),
        // Initialize room-level PRNG seed/nonce when the room is created
        serverSeed: randomBytes(32).toString('hex'),
        nonce: 0,
      };
      this.roomSessions.set(user.roomId, roomSession);

      logger.info('Room session created', {
        roomId: user.roomId,
        gameId: user.gameId
      });
    }

    roomSession.users.set(user.userId, session);
    roomSession.lastActivity = new Date();
  }

  /**
   * Remove user from room session
   * @param roomId - Room ID
   * @param userId - User ID
   */
  private removeUserFromRoom(roomId: string, userId: string): void {
    const roomSession = this.roomSessions.get(roomId);
    if (roomSession) {
      roomSession.users.delete(userId);

      // Remove room if empty
      if (roomSession.users.size === 0) {
        this.roomSessions.delete(roomId);
        logger.info('Room session removed (empty)', { roomId });
      }
    }
  }

  // /**
  //  * Move user to a different room
  //  * @param userId - User ID
  //  * @param newRoomId - New room ID
  //  * @param newGameId - New game ID (optional)
  //  */
  public moveUserToRoom(userId: string, newRoomId: string, newGameId?: string): boolean {
    const session = this.userSessions.get(userId);
    if (!session) {
      logger.warn('Cannot move user to room - session not found', { userId, newRoomId });
      return false;
    }

    const oldRoomId = session.user.roomId;

    // Remove from old room
    if (oldRoomId) {
      this.removeUserFromRoom(oldRoomId, userId);
      logger.info('User removed from old room', { userId, oldRoomId });
    }

    // Update user data
    session.user.roomId = newRoomId;
    if (newGameId) {
      session.user.gameId = newGameId;
    }

    // Add to new room
    this.addUserToRoom(session.user, session);

    logger.info('User moved to new room', {
      userId,
      oldRoomId,
      newRoomId,
      newGameId
    });

    return true;
  }

  /**
   * Get all active sessions (for debugging/monitoring)
   */
  public getStats() {
    return {
      userSessions: this.userSessions.size,
      roomSessions: this.roomSessions.size,
      socketMappings: this.socketToUser.size
    };
  }

  // Attach Socket.IO server instance
  public setIo(io: Server) {
    this.io = io;
  }

  // Compute a leaderboard for a room based on user sessions' game states
  public getRoomLeaderboard(roomId: string): Array<{ userId: string; name: string; score: number; lives: number; status: string }> {
    const roomSession = this.roomSessions.get(roomId);
    if (!roomSession) return [];

    const rows = Array.from(roomSession.users.values()).map(us => ({
      userId: us.user.userId,
      name: us.user.username,
      score: us.gameState?.score ?? 0,
      lives: us.gameState?.lives ?? 0,
      status: us.gameState?.status ?? 'waiting'
    }));

    rows.sort((a, b) => b.score - a.score);
    return rows;
  }

  // Emit leaderboard update to all users in the room, and per-user opponent snapshots
  public emitRoomLeaderboard(roomId: string): void {
    if (!this.io) return;
    const leaderboard = this.getRoomLeaderboard(roomId);
    this.io.to(roomId).emit('leaderboard_update', { roomId, leaderboard, timestamp: Date.now() });

    // Also emit per-user opponent info
    this.emitOpponentUpdates(roomId);
  }

  // For each user in room, emit an opponent snapshot to their socket
  private emitOpponentUpdates(roomId: string): void {
    if (!this.io) return;
    const roomSession = this.roomSessions.get(roomId);
    if (!roomSession) return;

    const users = Array.from(roomSession.users.values());
    for (const us of users) {
      const meId = us.user.userId;
      // Pick the highest-scoring other user as opponent (if any)
      const opponent = users
        .filter(x => x.user.userId !== meId)
        .map(x => ({
          userId: x.user.userId,
          name: x.user.username,
          score: x.gameState?.score ?? 0,
          lives: x.gameState?.lives ?? 0,
          status: x.gameState?.status ?? 'waiting'
        }))
        .sort((a, b) => b.score - a.score)[0];

      const payload = opponent
        ? { roomId, opponent, timestamp: Date.now() }
        : { roomId, opponent: null, timestamp: Date.now() };

      this.io.to(us.socketId).emit('opponent_update', payload);
    }
  }
}

// Singleton instance
export const sessionService = new SessionService();
