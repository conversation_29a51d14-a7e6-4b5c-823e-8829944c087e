import{e as g,u as d,h as c,i as m,j as b,k as i,g as p,l as h,m as k,o as v}from"./CBPGQi6i.js";function x(n=!1){const s=g,e=s.l.u;if(!e)return;let f=()=>h(s.s);if(n){let o=0,t={};const _=k(()=>{let l=!1;const r=s.s;for(const a in r)r[a]!==t[a]&&(t[a]=r[a],l=!0);return l&&o++,o});f=()=>p(_)}e.b.length&&d(()=>{u(s,f),i(e.b)}),c(()=>{const o=m(()=>e.m.map(b));return()=>{for(const t of o)typeof t=="function"&&t()}}),e.a.length&&c(()=>{u(s,f),i(e.a)})}function u(n,s){if(n.l.s)for(const e of n.l.s)p(e);s()}v();export{x as i};
